{"name": "skill_test_dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format:all": "prettier --write .", "format": "git diff --name-only --diff-filter=ACMRTUX | grep -E '\\.(js|ts|jsx|tsx|json|css|scss|md)$' | xargs prettier --write"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.39.1", "@upstash/redis": "^1.35.0", "axios": "^1.6.2", "bcryptjs": "^3.0.2", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "crypto": "^1.0.1", "html2canvas": "^1.4.1", "i18next": "^25.1.3", "iconsax-react": "^0.0.8", "jotai": "^2.9.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "katex": "^0.16.22", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "mermaid": "^11.6.0", "mongodb": "^6.17.0", "nanoid": "^5.1.5", "next": "14.0.4", "next-auth": "^4.24.7", "next-intl": "^3.17.6", "next-themes": "^0.4.6", "nuka-carousel": "^5.2.0", "prettier": "^3.6.2", "react": "^18", "react-collapsed": "^4.1.2", "react-dom": "^18", "react-hook-form": "^7.52.2", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "react-modern-drawer": "^1.4.0", "react-quill": "^2.0.0", "react-router-dom": "^6.21.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.7.0", "seedrandom": "^3.0.5", "sharp": "0.33.1", "sonner": "^2.0.6", "stripe": "^18.4.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-gradients": "^3.0.0", "zod": "^3.25.28"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "^8.6.5", "@storybook/addon-onboarding": "^8.6.5", "@storybook/blocks": "^8.6.5", "@storybook/experimental-addon-test": "^8.6.5", "@storybook/experimental-nextjs-vite": "8.6.5", "@storybook/react": "^8.6.5", "@storybook/test": "^8.6.5", "@types/lodash": "^4.17.15", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/seedrandom": "^3.0.8", "@vitest/browser": "^3.0.8", "@vitest/coverage-v8": "^3.0.8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "eslint-plugin-storybook": "^0.11.4", "playwright": "^1.51.0", "postcss": "^8", "storybook": "^8.6.5", "tailwindcss": "^3.3.0", "typescript": "^5", "vitest": "^3.0.8"}}