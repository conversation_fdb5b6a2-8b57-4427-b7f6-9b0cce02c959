@seed:login
Feature: User <PERSON><PERSON>: Successful login redirects to dashboard
    Given I am on the login page
    When I enter "<EMAIL>" and "password123"
    And I click the login button
    Then I should be redirected to the dashboard

  Scenario: <PERSON><PERSON> fails with wrong password
    Given I am on the login page
    When I enter "<EMAIL>" and "wrongpassword"
    And I click the login button
    Then I should see an error message "Invalid email or password"

  Sc<PERSON>rio: <PERSON><PERSON> fails with wrong email
    Given I am on the login page
    When I enter "<EMAIL>" and "password123"
    And I click the login button
    Then I should see an error message "Invalid email or password"
