const { Before, After } = require("@cucumber/cucumber")
const { cleanUpDb, seedDb } = require("./dbHooks")

Before(async function () {
  if (process.env.CLEANUP_DB) {
    await cleanUpDb()
  }
  await this.launch()
})

After(async function () {
  if (process.env.CLEANUP_DB) {
    await cleanUpDb()
  }
  await this.close()
})

Before({ tags: "@seed:users_free" }, async function () {
  await seedDb("users", [
    {
      email: "<EMAIL>",
      password: "password123",
      name: "Test User",
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ])
})

// Seed for premium test data
Before({ tags: "@seed:premiumTest" }, async function () {
  await seedDb("tests", [
    {
      title: "Premium Test",
      description: "This is a premium test",
      access: "PUBLIC",
      status: "ACTIVE",
      organizationId: "org_123",
      categories: ["Category 1"],
      difficulties: ["EASY"],
      benefits: ["Benefit 1"],
      usersCount: 0,
      successRate: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ])
})
