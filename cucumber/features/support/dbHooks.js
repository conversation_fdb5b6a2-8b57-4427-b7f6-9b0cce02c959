async function cleanUpDb() {
  try {
    const response = await fetch("http://localhost:3000/api/v1/test/db/clean", {
      method: "POST",
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.errors?.[0] || "Failed to clean up database")
    }

    const result = await response.json()
  } catch (error) {
    console.error("Failed to clean up database:", error)
  }
}

async function seedDb(collection, data) {
  try {
    const response = await fetch("http://localhost:3000/api/v1/test/db/seed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        collection,
        data,
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.errors?.[0] || "Failed to seed database")
    }

    const result = await response.json()
  } catch (error) {
    console.error("Failed to seed database:", error)
  }
}

module.exports = {
  cleanUpDb,
  seedDb,
}
