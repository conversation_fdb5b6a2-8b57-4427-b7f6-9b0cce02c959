Feature: Free Account Test Creator User Journey
  As a user with a free account
  I want to create and manage tests
  So that I can evaluate candidates effectively

  @user-journey @free-account @test-creator
  <PERSON><PERSON>rio: Complete Free Account Test Creator Experience
    # Setup test environment
    Given the test environment is set up

    # Registration and Email Verification
    Given I am on the registration page
    When I enter name "<PERSON>", email "<EMAIL>", and password "SecurePass123!"
    And I accept the terms and conditions
    And I click the register button
    Then I should see a success message "Registration successful"

    # First Login and Onboarding
    Given I am on the login page
    When I enter "<EMAIL>" and "SecurePass123!"
    And I click the login button
    Then I should be redirected to the dashboard

    # Create First Question
    Given I am on the question bank page
    When I click "Create Question"
    And I select question type "multiple choice"
    And I enter question text "What is the capital of France?"
    And I add option "Paris" as correct answer
    And I add option "London" as incorrect answer
    And I add option "Berlin" as incorrect answer
    And I add option "Madrid" as incorrect answer
    And I add tags "geography, capitals"
    And I click the save button
    Then I should see "Question created successfully"

    # Create Additional Questions (Testing Limits)
    When I create 9 more basic questions
    Then I should see "10 of 10 questions used" in the free plan counter
    When I try to create another question
    Then I should see "Free plan limit reached. Upgrade to Pro to create unlimited questions"

    # Create First Test
    Given I am on the test creation page
    When I enter test name "Geography Basics"
    And I enter test description "Test your knowledge of world geography"
    And I set test duration to "15 minutes"
    And I select difficulty "Easy"
    And I click "Create Test"
    Then I should see "Test created successfully"
    And I should be redirected to the test editor

    # Add Questions to Test
    Given I am in the test editor for "Geography Basics"
    When I search for questions with tag "geography"
    And I add the question "What is the capital of France?" to the test
    And I add 4 more geography questions to the test
    And I click "Save Test"
    Then I should see "Test saved successfully"
    And I should see "5 questions added to test"

    # Configure Test Settings
    When I click "Test Settings"
    And I set access to "PUBLIC"
    And I click "Save"
    Then I should see "Test settings updated"

    # Preview Test
    When I click "Preview Test"
    Then I should see the test as students would see it
    And I should see all 5 questions
    And I should see the test timer
    And I should see "Geography Basics" as the test title

    # Publish Test
    When I click "Publish Test"
    Then I should see "Test published successfully"
    And the test status should change to "ACTIVE"
    And I should see the public test link

    # Take Own Test (as Test Creator)
    When I open the public test link in a new tab
    And I enter name "John Doe" and email "<EMAIL>"
    And I click "Start Test"
    Then I should see the test instructions
    And I should see "Geography Basics" test

    When I click "Begin Test"
    And I answer all questions correctly
    And I click "Submit Test"
    Then I should see "Test submitted successfully"
    And I should see my test results
    And I should see "Score: 100%"
    And I should see "Status: Passed"

    # View Test Analytics (Free Limitations)
    Given I am back on the dashboard as the test creator
    When I go to the test analytics page for "Geography Basics"
    Then I should see basic analytics
    And I should see "1 test attempt"
    And I should see "100% average score"
    But I should see "Upgrade to Pro for detailed analytics"
    And I should see limited analytics features
