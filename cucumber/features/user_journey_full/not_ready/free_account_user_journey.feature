Feature: Free Account User Journey
  As a new user with a free account
  I want to experience the complete platform workflow
  So that I can understand the value and consider upgrading

  @user-journey @free-account @smoke
  Scenario: Complete Free Account User Experience
    # Registration and Email Verification
    Given I am on the registration page
    When I enter name "<PERSON>", email "<EMAIL>", and password "SecurePass123!"
    And I accept the terms and conditions
    And I click the register button
    Then I should see a success message "Registration successful"
    And I should receive a verification email at "<EMAIL>"
    
    When I click the verification link in the email
    Then I should see "Email verified successfully"
    And I should be redirected to the login page

    # First Login and Onboarding
    Given I am on the login page
    When I enter "<EMAIL>" and "SecurePass123!"
    And I click the login button
    Then I should be redirected to the dashboard
    And I should see a welcome message for new users
    And I should see the free plan limitations displayed

    # Profile Setup
    Given I am on the dashboard
    When I click "Complete Profile Setup"
    And I enter organization name "<PERSON>'s Learning Hub"
    And I select industry "Education"
    And I upload a profile picture
    And I click "Save Profile"
    Then I should see "Profile completed successfully"
    And I should see my organization dashboard

    # Explore Question Bank (Free Limitations)
    Given I am on the question bank page
    When I click "Create Question"
    Then I should see the question editor
    When I select question type "multiple choice"
    And I enter question text "What is the capital of France?"
    And I add option "Paris" as correct answer
    And I add option "London" as incorrect answer
    And I add option "Berlin" as incorrect answer
    And I add option "Madrid" as incorrect answer
    And I add tags "geography, capitals"
    And I click the save button
    Then I should see "Question created successfully"
    And I should see "1 of 10 questions used" in the free plan counter

    # Create Additional Questions (Testing Limits)
    When I create 9 more basic questions
    Then I should see "10 of 10 questions used" in the free plan counter
    When I try to create another question
    Then I should see "Free plan limit reached. Upgrade to Pro to create unlimited questions"
    And I should see an "Upgrade Now" button

    # Create First Test
    Given I am on the test creation page
    When I enter test name "Geography Basics"
    And I enter test description "Test your knowledge of world geography"
    And I set test duration to "15 minutes"
    And I select difficulty "Easy"
    And I click "Create Test"
    Then I should see "Test created successfully"
    And I should be redirected to the test editor

    # Add Questions to Test
    Given I am in the test editor for "Geography Basics"
    When I search for questions with tag "geography"
    And I add the question "What is the capital of France?" to the test
    And I add 4 more geography questions to the test
    And I click "Save Test"
    Then I should see "Test saved successfully"
    And I should see "5 questions added to test"

    # Configure Test Settings
    When I click "Test Settings"
    And I set access to "PUBLIC"
    And I enable "Show results immediately"
    And I set passing score to "70%"
    And I generate an invitation code
    And I click "Save Settings"
    Then I should see "Test settings updated"
    And I should see the generated invitation code

    # Preview Test
    When I click "Preview Test"
    Then I should see the test as students would see it
    And I should see all 5 questions
    And I should see the test timer
    And I should see "Geography Basics" as the test title

    # Publish Test
    When I click "Publish Test"
    Then I should see "Test published successfully"
    And the test status should change to "ACTIVE"
    And I should see the public test link

    # Share Test
    When I click "Share Test"
    Then I should see sharing options
    And I should see the invitation code
    And I should see the public test URL
    When I copy the test link
    Then I should see "Link copied to clipboard"

    # Take Own Test (as Test Creator)
    When I open the public test link in a new tab
    And I enter name "Sarah Johnson" and email "<EMAIL>"
    And I click "Start Test"
    Then I should see the test instructions
    And I should see "Geography Basics" test
    
    When I click "Begin Test"
    And I answer all questions correctly
    And I click "Submit Test"
    Then I should see "Test submitted successfully"
    And I should see my test results
    And I should see "Score: 100%"
    And I should see "Status: Passed"

    # View Test Analytics (Free Limitations)
    Given I am back on the dashboard as the test creator
    When I go to the test analytics page for "Geography Basics"
    Then I should see basic analytics
    And I should see "1 test attempt"
    And I should see "100% average score"
    But I should see "Upgrade to Pro for detailed analytics"
    And I should see limited analytics features

    # Explore Collaboration Features (Free Limitations)
    Given I am on the collaboration settings page
    When I try to invite a team member
    Then I should see "Free plan allows 1 team member only"
    And I should see "Upgrade to Pro for unlimited team members"
    When I click "Upgrade to Pro"
    Then I should see the pricing page
    And I should see Pro plan benefits

    # Attempt to Create Second Test (Free Limitation)
    Given I am on the test creation page
    When I try to create a second test
    Then I should see "Free plan allows 1 test only"
    And I should see "Upgrade to Pro for unlimited tests"
    And I should see upgrade options

    # Explore Settings and Profile
    Given I am on the settings page
    When I update my notification preferences
    And I change my theme to "dark mode"
    And I update my profile information
    Then I should see "Settings updated successfully"
    And the interface should switch to dark mode

    # View Usage Dashboard
    Given I am on the usage dashboard
    Then I should see my current plan usage
    And I should see "Questions: 10/10 used"
    And I should see "Tests: 1/1 used"
    And I should see "Team Members: 1/1 used"
    And I should see "Storage: 50MB/100MB used"
    And I should see upgrade recommendations

    # Experience Upgrade Prompts
    When I try to access premium features
    Then I should see contextual upgrade prompts
    And I should see "Unlock this feature with Pro"
    When I click "Learn More"
    Then I should see detailed Pro plan benefits
    And I should see pricing information

    # Test Mobile Experience
    Given I am using a mobile device
    When I access the dashboard
    Then the interface should be mobile-responsive
    And all features should work on mobile
    When I take a test on mobile
    Then the test interface should be touch-friendly
    And I should be able to complete the test successfully

    # Logout and Re-login
    When I logout from the application
    Then I should be redirected to the home page
    When I login again with "<EMAIL>" and "SecurePass123!"
    Then I should be redirected to the dashboard
    And all my data should be preserved
    And I should see my created test "Geography Basics"

    # Final Verification
    Then I should have successfully completed the free account journey
    And I should understand the platform's value proposition
    And I should be aware of Pro plan benefits
    And I should have a functional test ready for sharing
