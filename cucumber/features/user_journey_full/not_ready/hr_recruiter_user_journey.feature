Feature: HR Recruiter User Journey
  As an HR recruiter
  I want to streamline the technical hiring process
  So that I can efficiently identify and evaluate qualified candidates

  @user-journey @hr-recruiter @hiring
  Scenario: Complete HR Recruiter Hiring Workflow
    # HR Recruiter Onboarding
    Given I am an HR recruiter at "TechStartup Inc"
    When I register with email "<EMAIL>"
    And I complete the HR professional onboarding
      | Field                | Value                    |
      | Company Name         | TechStartup Inc          |
      | Industry             | Software Development     |
      | Company Size         | 50-200 employees         |
      | Hiring Volume        | 20-30 hires per quarter  |
      | Primary Roles        | Developers, Designers    |
      | Current ATS          | Greenhouse               |
      | Integration Needs    | ATS, Slack, Calendar     |
    Then I should see the HR recruiter dashboard
    And I should see hiring-focused features and workflows

    # Job Requisition and Assessment Planning
    Given I receive a new job requisition for "Senior Frontend Developer"
    When I create a hiring plan for the position
    And I define the role requirements
      | Requirement          | Details                           |
      | Experience Level     | 5+ years                         |
      | Technical Skills     | React, JavaScript, TypeScript    |
      | Soft Skills          | Communication, Problem-solving   |
      | Team Fit             | Collaborative, Self-motivated    |
      | Education            | Bachelor's degree preferred      |
      | Location             | Remote or San Francisco          |
    And I select appropriate assessment templates
      | Assessment Type      | Purpose                          |
      | Technical Screening  | Initial technical evaluation     |
      | Coding Challenge     | Hands-on programming skills      |
      | System Design       | Architecture and scaling         |
      | Cultural Fit        | Team dynamics and values         |
    Then I should see "Hiring plan created for Senior Frontend Developer"

    # Candidate Sourcing and Initial Screening
    Given I am sourcing candidates for the frontend role
    When I post the job on multiple platforms
    And I receive 150 applications
    And I use AI-powered resume screening
    Then I should see candidates ranked by fit score
      | Candidate           | Fit Score | Experience | Skills Match |
      | Sarah Chen          | 92%       | 6 years    | 95%         |
      | Michael Rodriguez   | 88%       | 5 years    | 90%         |
      | Jennifer Kim        | 85%       | 7 years    | 85%         |
      | David Thompson      | 82%       | 4 years    | 88%         |
    And I should see "45 candidates qualified for technical screening"

    # Automated Assessment Invitation
    Given I have qualified candidates
    When I send automated assessment invitations
    And I customize the invitation email template
      | Element              | Content                           |
      | Subject Line         | Next Step: Technical Assessment   |
      | Personal Greeting    | Hi [Candidate Name]               |
      | Company Introduction | About TechStartup Inc             |
      | Assessment Details   | 90-minute technical evaluation    |
      | Timeline             | Complete within 3 days           |
      | Support Contact      | Technical support available       |
    And I schedule invitations to be sent in batches
    Then I should see "45 assessment invitations scheduled"
    And candidates should receive professional, branded emails

    # Candidate Assessment Monitoring
    Given candidates are taking technical assessments
    When I monitor assessment progress in real-time
    Then I should see live candidate activity
      | Metric                | Value |
      | Invitations Sent      | 45    |
      | Assessments Started   | 38    |
      | Assessments Completed | 32    |
      | In Progress          | 6     |
      | Average Score        | 74%   |
      | Average Duration     | 78min |
      | Completion Rate      | 84%   |
    And I should see individual candidate progress
    And I should be able to provide support if needed

    # Assessment Results Analysis
    Given candidates have completed assessments
    When I review the assessment results
    Then I should see comprehensive candidate evaluation
      | Candidate           | Overall Score | Technical | Problem Solving | Code Quality |
      | Sarah Chen          | 94%          | 96%       | 92%            | 95%         |
      | Michael Rodriguez   | 89%          | 87%       | 91%            | 88%         |
      | Jennifer Kim        | 86%          | 84%       | 88%            | 87%         |
      | David Thompson      | 78%          | 75%       | 82%            | 76%         |
    And I should see detailed skill breakdowns
    And I should see code review comments from technical evaluators

    # Candidate Ranking and Shortlisting
    Given I have assessment results
    When I apply additional filtering criteria
      | Criteria             | Weight | Threshold |
      | Technical Score      | 40%    | 80%       |
      | Problem Solving      | 30%    | 75%       |
      | Code Quality         | 20%    | 80%       |
      | Communication        | 10%    | 70%       |
    And I consider diversity and inclusion factors
    And I review candidate portfolios and GitHub profiles
    Then I should see "12 candidates shortlisted for interviews"
    And I should have a ranked list of top candidates

    # Interview Scheduling Automation
    Given I have shortlisted candidates
    When I initiate the interview scheduling process
    And I configure interview rounds
      | Round               | Duration | Interviewers                    |
      | Technical Deep Dive | 60 min   | Senior Developer, Tech Lead     |
      | System Design      | 45 min   | Engineering Manager             |
      | Cultural Fit       | 30 min   | Team Members, HR               |
      | Final Round        | 45 min   | CTO, Hiring Manager             |
    And I integrate with calendar systems
    And I send automated scheduling links to candidates
    Then I should see "Interview schedules coordinated for 12 candidates"

    # Interview Feedback Collection
    Given interviews are being conducted
    When I collect feedback from interviewers
    Then I should see structured interview evaluations
      | Candidate      | Technical | Communication | Culture Fit | Recommendation |
      | Sarah Chen     | Strong    | Excellent     | Strong      | Strong Hire    |
      | Michael R.     | Strong    | Good          | Good        | Hire           |
      | Jennifer Kim   | Good      | Excellent     | Strong      | Hire           |
    And I should see detailed interviewer comments
    And I should see consensus recommendations

    # Reference Checks and Background Verification
    Given I have top candidates
    When I initiate reference checks for final candidates
    And I use automated reference check tools
    And I verify employment history and education
    Then I should see comprehensive candidate verification
      | Candidate    | References | Background | Education | Employment |
      | Sarah Chen   | Excellent  | Clear      | Verified  | Verified   |
      | Michael R.   | Good       | Clear      | Verified  | Verified   |
    And I should have confidence in candidate qualifications

    # Offer Management and Negotiation
    Given I'm ready to make offers
    When I prepare offer packages for top candidates
    And I use market data for competitive compensation
      | Component        | Sarah Chen | Michael R. |
      | Base Salary      | $140,000   | $130,000   |
      | Equity           | 0.15%      | 0.12%      |
      | Signing Bonus    | $10,000    | $8,000     |
      | Benefits Package | Full       | Full       |
      | Start Date       | Flexible   | Flexible   |
    And I send personalized offer letters
    Then I should see "2 offers extended"
    And I should track offer acceptance rates

    # Candidate Experience Management
    Given I want to ensure positive candidate experience
    When I collect candidate feedback throughout the process
    Then I should see candidate satisfaction metrics
      | Stage                | Satisfaction | Feedback                        |
      | Application Process  | 4.2/5        | "Smooth and user-friendly"      |
      | Technical Assessment | 4.0/5        | "Fair and relevant questions"    |
      | Interview Process    | 4.5/5        | "Professional and respectful"   |
      | Communication        | 4.3/5        | "Timely and informative"        |
    And I should address any concerns promptly
    And I should maintain positive employer brand

    # Onboarding Preparation
    Given Sarah Chen accepts the offer
    When I initiate the onboarding process
    And I coordinate with IT for equipment setup
    And I schedule orientation sessions
    And I prepare welcome materials
      | Material             | Status    |
      | Welcome Package      | Prepared  |
      | Equipment Order      | Submitted |
      | Access Credentials   | Generated |
      | Buddy Assignment     | Completed |
      | First Week Schedule  | Created   |
    Then I should see "Onboarding prepared for Sarah Chen"

    # Hiring Analytics and Reporting
    Given I want to analyze hiring effectiveness
    When I generate hiring reports for the quarter
    Then I should see comprehensive hiring metrics
      | Metric                    | Q3 2024 | Target | Trend |
      | Time to Hire              | 28 days | 30     | ↗     |
      | Cost per Hire             | $3,200  | $4,000 | ↗     |
      | Offer Acceptance Rate     | 85%     | 80%    | ↗     |
      | Quality of Hire Score     | 4.2/5   | 4.0    | ↗     |
      | Candidate Satisfaction    | 4.3/5   | 4.0    | ↗     |
      | Diversity Hiring Rate     | 45%     | 40%    | ↗     |
    And I should see ROI from assessment platform usage

    # Process Optimization and Improvement
    Given I want to continuously improve hiring
    When I analyze bottlenecks in the hiring process
    And I identify areas for improvement
      | Area                  | Issue                    | Solution                     |
      | Assessment Completion | 16% dropout rate         | Shorter initial screening    |
      | Interview Scheduling  | 3-day average delay      | Better calendar integration  |
      | Offer Response Time   | 5-day average            | Faster decision making       |
    And I implement process improvements
    Then I should see measurable improvements in hiring efficiency

    # Team Collaboration and Communication
    Given I work with hiring managers and technical teams
    When I facilitate collaboration between stakeholders
    And I provide regular updates on hiring progress
    And I coordinate feedback sessions
    Then I should see improved alignment between HR and technical teams
    And I should have better hiring outcomes

    # Compliance and Legal Considerations
    Given I need to ensure legal compliance
    When I review hiring practices for bias and fairness
    And I maintain proper documentation
    And I ensure EEOC compliance
    Then I should have defensible hiring decisions
    And I should minimize legal risks

    # Talent Pipeline Development
    Given I want to build long-term talent pipelines
    When I maintain relationships with promising candidates
    And I create talent pools for future openings
    And I engage with universities and coding bootcamps
    Then I should have a strong pipeline for future hiring needs
    And I should reduce time-to-fill for new positions

    # Technology Integration and Automation
    Given I want to leverage technology for efficiency
    When I integrate assessment platform with our ATS
    And I set up automated workflows
    And I use AI for candidate matching
    Then I should see significant time savings
    And I should have more time for strategic activities

    # Final HR Recruiter Journey Validation
    Then I should have successfully hired qualified candidates
    And I should have efficient, scalable hiring processes
    And I should see positive candidate experience metrics
    And I should have strong relationships with hiring managers
    And I should contribute to company growth through quality hires
    And I should have data-driven insights for continuous improvement
