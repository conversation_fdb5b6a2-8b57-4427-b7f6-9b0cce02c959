Feature: Public Test Taker User Journey
  As a candidate taking a public test
  I want to have a smooth and intuitive test-taking experience
  So that I can demonstrate my skills effectively

  @user-journey @test-taker @public-test
  Scenario: Complete Public Test Taker Experience
    # Discover Public Test
    Given a public test "JavaScript Developer Assessment" exists
    And the test has invitation code "JS2024DEV"
    And the test contains 20 questions of mixed types
    When I visit the public test discovery page
    Then I should see available public tests
    And I should see "JavaScript Developer Assessment" in the list

    # Access Test via Invitation Code
    Given I receive an invitation code "JS2024DEV" via email
    When I visit the join test page
    And I enter invitation code "JS2024DEV"
    Then I should see the test information page
    And I should see test details
      | Field           | Value                              |
      | Test Name       | JavaScript Developer Assessment    |
      | Duration        | 90 minutes                        |
      | Questions       | 20 questions                      |
      | Difficulty      | Intermediate                      |
      | Passing Score   | 70%                               |
      | Attempts Allowed| 1                                 |

    # Guest Registration for Test
    When I click "Take This Test"
    Then I should see the guest registration form
    When I enter my details
      | Field           | Value                    |
      | Full Name       | Alex Rodriguez           |
      | Email Address   | <EMAIL> |
      | Phone Number    | ******-0123             |
      | Experience Level| 3 years                  |
      | Current Role    | Frontend Developer       |
    And I accept the test terms and conditions
    And I click "Register and Continue"
    Then I should see "Registration successful"
    And I should receive a confirmation email

    # Pre-Test Instructions and System Check
    Given I am registered for the test
    When I proceed to the test preparation page
    Then I should see detailed test instructions
    And I should see system requirements check
      | Requirement     | Status  |
      | Browser Version | ✓ Pass  |
      | Internet Speed  | ✓ Pass  |
      | Camera Access   | ✓ Pass  |
      | Microphone      | ✓ Pass  |
      | Screen Size     | ✓ Pass  |

    When I run the system compatibility test
    Then I should see "System check completed successfully"
    And I should see "You're ready to take the test"

    # Test Environment Setup
    When I click "Enter Test Environment"
    Then I should see the secure test environment
    And I should see the test timer (90:00)
    And I should see question navigation panel
    And I should see "Question 1 of 20"
    And I should see the test progress bar

    # Taking Different Question Types
    
    # Multiple Choice Question
    Given I am on question 1
    When I see the question "Which of the following is a JavaScript framework?"
    And I see the options
      | Option | Text        |
      | A      | React       |
      | B      | Python      |
      | C      | MySQL       |
      | D      | Photoshop   |
    And I select option "A" (React)
    And I click "Next Question"
    Then I should move to question 2
    And my answer should be saved automatically

    # Code Input Question
    Given I am on question 5
    When I see the code question "Write a function to find the largest number in an array"
    And I see the code editor with JavaScript syntax highlighting
    When I write the code solution
      """
      function findLargest(arr) {
        if (arr.length === 0) return null;
        return Math.max(...arr);
      }
      """
    And I click "Run Tests"
    Then I should see test case results
      | Test Case              | Status |
      | findLargest([1,5,3])   | ✓ Pass |
      | findLargest([])        | ✓ Pass |
      | findLargest([-1,-5])   | ✓ Pass |
    And I click "Save and Continue"

    # Text Input Question
    Given I am on question 10
    When I see the question "Explain the difference between let, const, and var in JavaScript"
    And I see a text area for my response
    When I type my detailed answer
      """
      var: Function-scoped, can be redeclared and updated, hoisted with undefined
      let: Block-scoped, can be updated but not redeclared, hoisted but not initialized
      const: Block-scoped, cannot be updated or redeclared, must be initialized at declaration
      """
    And I click "Save and Continue"
    Then my text answer should be saved

    # Image-Based Question
    Given I am on question 15
    When I see an image showing a code snippet with an error
    And I see the question "What is wrong with this code?"
    And I see multiple choice options about potential issues
    When I analyze the image and select the correct answer
    And I click "Next Question"
    Then my answer should be recorded

    # Test Navigation and Review
    When I reach question 20
    And I complete the final question
    Then I should see "All questions completed"
    And I should see the review page with question summary
      | Status      | Count |
      | Answered    | 20    |
      | Unanswered  | 0     |
      | Flagged     | 2     |

    # Review Flagged Questions
    When I click on a flagged question
    Then I should be taken back to that question
    And I should be able to modify my answer
    When I update my answer and remove the flag
    And I return to the review page
    Then the flagged count should decrease

    # Final Submission
    When I click "Submit Test"
    Then I should see a submission confirmation dialog
    And I should see "Are you sure you want to submit? You cannot change answers after submission."
    When I click "Yes, Submit Test"
    Then I should see "Test submitted successfully"
    And I should see the submission timestamp

    # Immediate Results (if enabled)
    Given the test is configured to show immediate results
    When the test is submitted
    Then I should see my test results
      | Metric              | Value     |
      | Overall Score       | 85%       |
      | Status              | Passed    |
      | Time Taken          | 78 minutes|
      | Questions Correct   | 17/20     |
      | Percentile Ranking  | 82nd      |

    # Detailed Score Breakdown
    When I view the detailed results
    Then I should see section-wise performance
      | Section           | Score | Questions |
      | Multiple Choice   | 90%   | 9/10      |
      | Code Problems     | 80%   | 4/5       |
      | Text Responses    | 85%   | 4/5       |
    And I should see individual question feedback where available

    # Certificate Generation
    Given I passed the test with 85%
    When the results are processed
    Then I should see "Certificate Available"
    When I click "Download Certificate"
    Then I should receive a PDF certificate
    And the certificate should contain
      | Field           | Value                           |
      | Candidate Name  | Alex Rodriguez                  |
      | Test Name       | JavaScript Developer Assessment |
      | Score           | 85%                            |
      | Date Completed  | Current date                   |
      | Certificate ID  | Unique identifier              |

    # Post-Test Communication
    Given I completed the test
    When I check my email
    Then I should receive a completion confirmation email
    And the email should contain
      | Content                    |
      | Test completion confirmation|
      | Score summary              |
      | Certificate download link  |
      | Next steps information     |
      | Contact information        |

    # Test History and Profile
    When I create an account using the same email
    And I login to the platform
    Then I should see my test history
    And I should see "JavaScript Developer Assessment" in my completed tests
    When I click on the test entry
    Then I should see detailed test information and results

    # Feedback and Rating
    Given I completed the test
    When I am prompted to provide feedback
    And I rate the test experience
      | Aspect              | Rating |
      | Question Quality    | 5/5    |
      | User Interface      | 4/5    |
      | Instructions        | 5/5    |
      | Technical Issues    | 5/5    |
      | Overall Experience  | 5/5    |
    And I provide written feedback
      """
      Great test experience! Questions were relevant and the interface was intuitive.
      The code editor worked perfectly and the system was very responsive.
      """
    And I submit the feedback
    Then I should see "Thank you for your feedback"

    # Mobile Test Experience
    Given I want to take a test on my mobile device
    When I access the test on a smartphone
    Then the interface should be mobile-responsive
    And all question types should work on mobile
    And the code editor should be touch-friendly
    When I complete the test on mobile
    Then the experience should be equivalent to desktop

    # Accessibility Features
    Given I have accessibility needs
    When I enable accessibility features
      | Feature           | Status  |
      | Screen Reader     | Enabled |
      | High Contrast     | Enabled |
      | Large Text        | Enabled |
      | Keyboard Nav      | Enabled |
    Then the test should be fully accessible
    And I should be able to complete the test using assistive technology

    # Test Interruption and Resume
    Given I am taking a test
    When my internet connection is interrupted
    Then I should see "Connection lost" notification
    When my connection is restored
    Then I should see "Connection restored"
    And my progress should be automatically saved
    And I should be able to continue from where I left off

    # Proctoring Experience (if enabled)
    Given the test has proctoring enabled
    When I start the test
    Then I should see proctoring notifications
    And my camera should be activated
    And I should see "Test is being monitored"
    When I complete the test under proctoring
    Then the proctoring data should be recorded
    And I should see "Proctoring completed successfully"

    # Final Validation
    Then I should have completed the full test-taker journey
    And I should have a positive testing experience
    And I should receive my results and certificate
    And I should be able to share my achievement
    And I should have clear next steps for follow-up
