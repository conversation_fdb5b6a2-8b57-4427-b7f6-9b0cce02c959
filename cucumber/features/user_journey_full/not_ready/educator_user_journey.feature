Feature: Educator User Journey
  As an educator or trainer
  I want to create and manage educational assessments
  So that I can evaluate student learning and track progress effectively

  @user-journey @educator @education
  Scenario: Complete Educator Experience for Student Assessment
    # Educator Registration and Setup
    Given I am an educator at "Springfield University"
    When I register with email "<EMAIL>"
    And I select account type "Educational Institution"
    And I enter institution details
      | Field              | Value                    |
      | Institution Name   | Springfield University   |
      | Department         | Computer Science         |
      | Role               | Professor                |
      | Student Count      | 150 students per semester|
      | Courses Taught     | Programming, Algorithms   |
    And I verify my educational email address
    Then I should receive educational pricing and features
    And I should see "Educational account activated"

    # Course and Class Setup
    Given I am logged into my educator dashboard
    When I create my course structure
      | Course Code | Course Name              | Semester | Students |
      | CS101       | Introduction to Programming | Fall 2024| 45      |
      | CS201       | Data Structures          | Fall 2024| 38      |
      | CS301       | Algorithms               | Fall 2024| 32      |
    And I import student rosters from CSV files
      | Student ID | Name           | Email                    | Course |
      | 12345      | Alice <PERSON>  | <EMAIL>      | CS101  |
      | 12346      | <PERSON>      | <EMAIL>        | CS101  |
      | 12347      | <PERSON>    | <EMAIL>      | CS201  |
    Then I should see "3 courses created with 115 students enrolled"

    # Learning Objectives and Assessment Planning
    Given I am planning assessments for CS101
    When I define learning objectives
      | Week | Topic                    | Learning Objectives                           |
      | 1    | Variables and Data Types | Understand primitive types, declare variables |
      | 2    | Control Structures       | Use if/else, loops effectively               |
      | 3    | Functions               | Write and call functions, understand scope    |
      | 4    | Arrays                  | Manipulate arrays, understand indexing       |
    And I map assessments to learning objectives
    Then I should see "Learning objectives defined for 4 weeks"

    # Create Formative Assessments (Weekly Quizzes)
    Given I want to create weekly knowledge checks
    When I create a formative assessment for Week 1
    And I add questions aligned with learning objectives
      | Question Type    | Question                                    | Points |
      | Multiple Choice  | Which is a valid variable declaration?      | 2      |
      | Code Completion  | Complete the variable assignment            | 3      |
      | True/False       | Variables must be initialized when declared | 1      |
      | Short Answer     | Explain the difference between int and float| 4      |
    And I set assessment parameters
      | Parameter        | Value                    |
      | Time Limit       | 15 minutes               |
      | Attempts Allowed | 2                        |
      | Availability     | Week 1 (Mon-Fri)         |
      | Feedback         | Immediate with explanations |
      | Weight           | 5% of final grade        |
    Then I should see "Week 1 quiz created successfully"

    # Create Summative Assessment (Midterm Exam)
    Given I need to create a comprehensive midterm exam
    When I create a summative assessment covering weeks 1-6
    And I use question pools for randomization
      | Topic              | Pool Size | Questions per Student |
      | Variables          | 20        | 3                     |
      | Control Structures | 25        | 4                     |
      | Functions          | 30        | 5                     |
      | Arrays             | 20        | 3                     |
    And I add coding problems with auto-grading
      | Problem                    | Test Cases | Time Limit |
      | Write a function to sum array | 5        | 20 minutes |
      | Implement bubble sort      | 8          | 30 minutes |
    And I configure exam settings
      | Setting              | Value                    |
      | Duration             | 90 minutes               |
      | Proctoring           | Enabled                  |
      | Question Randomization| Enabled                  |
      | Browser Lockdown     | Enabled                  |
      | Attempts             | 1                        |
      | Weight               | 25% of final grade       |
    Then I should see "Midterm exam configured successfully"

    # Student Communication and Instructions
    Given I need to communicate with students about assessments
    When I create announcement for the upcoming quiz
    And I send detailed instructions via the platform
      | Content                                                    |
      | Week 1 Quiz is now available                               |
      | Topics covered: Variables and Data Types                   |
      | Duration: 15 minutes, 2 attempts allowed                   |
      | Available: Monday 9 AM - Friday 11:59 PM                  |
      | Study materials: Chapter 1, lecture slides, practice problems |
    And I schedule reminder notifications
    Then students should receive notifications
    And I should see "Announcement sent to 45 students"

    # Monitor Student Progress in Real-Time
    Given students are taking the Week 1 quiz
    When I access the real-time monitoring dashboard
    Then I should see live assessment statistics
      | Metric              | Value |
      | Students Started    | 32    |
      | Students Completed  | 28    |
      | Students In Progress| 4     |
      | Average Score       | 78%   |
      | Average Time        | 12min |
      | Completion Rate     | 87%   |
    And I should see individual student progress
    And I should be able to provide real-time assistance

    # Automated Grading and Feedback
    Given students have completed the quiz
    When the auto-grading system processes submissions
    Then I should see detailed grading results
      | Student        | Score | Time Taken | Attempts Used |
      | Alice Johnson  | 92%   | 11 minutes | 1             |
      | Bob Smith      | 76%   | 14 minutes | 2             |
      | Carol Davis    | 88%   | 9 minutes  | 1             |
    And students should receive immediate feedback
    And I should see areas where students struggled

    # Manual Grading for Open-Ended Questions
    Given there are short answer questions requiring manual review
    When I access the grading interface
    Then I should see student responses organized for efficient grading
    And I should see suggested rubrics and sample answers
    When I grade responses and provide personalized feedback
      | Student        | Response Quality | Feedback                                    |
      | Alice Johnson  | Excellent        | Great understanding of data types!          |
      | Bob Smith      | Good             | Good answer, consider mentioning precision  |
      | Carol Davis    | Needs Work       | Review the difference between int and float |
    Then I should see "Manual grading completed for 45 students"

    # Analytics and Learning Insights
    Given I want to analyze student performance
    When I access the assessment analytics dashboard
    Then I should see comprehensive learning analytics
      | Metric                    | Value | Insight                           |
      | Class Average             | 82%   | Above target of 75%               |
      | Question Difficulty       | Varied| Q3 was too difficult (45% correct)|
      | Learning Objective Mastery| 78%   | Variables concept well understood |
      | Time Distribution         | Normal| Most students used 10-13 minutes  |
    And I should see individual student learning paths
    And I should see recommendations for intervention

    # Adaptive Learning and Remediation
    Given some students need additional support
    When I identify struggling students based on assessment data
    And I create personalized remediation plans
      | Student     | Weakness Area    | Remediation Plan                    |
      | Bob Smith   | Data Types       | Additional practice problems        |
      | John Doe    | Control Flow     | One-on-one tutoring session        |
      | Mary Wilson | Functions        | Peer tutoring with advanced student |
    And I assign adaptive practice exercises
    Then students should receive personalized learning paths
    And I should track their improvement over time

    # Collaborative Assessment Creation
    Given I want to collaborate with other educators
    When I invite my teaching assistant to help create assessments
    And I share question banks with department colleagues
    And I participate in peer review of assessment quality
    Then I should see improved assessment quality
    And I should have access to a broader range of questions

    # Parent/Guardian Communication
    Given I need to communicate with parents about student progress
    When I generate progress reports for struggling students
    And I schedule parent-teacher conferences
    And I send automated progress updates
    Then parents should be informed about their child's academic progress
    And I should see improved student engagement

    # Accessibility and Accommodations
    Given I have students with special needs
    When I configure accessibility accommodations
      | Student        | Accommodation                    |
      | Sarah Lee      | Extended time (1.5x)            |
      | Mike Johnson   | Screen reader compatibility      |
      | Lisa Chen      | Large font size                 |
    And I ensure all assessments are accessible
    Then all students should be able to participate equally
    And I should meet institutional accessibility requirements

    # Grade Book Integration and Final Grades
    Given the semester is ending
    When I review all assessment scores
    And I calculate final grades using weighted averages
      | Assessment Type | Weight | Student Average |
      | Weekly Quizzes  | 20%    | 85%            |
      | Midterm Exam    | 25%    | 78%            |
      | Final Project   | 30%    | 82%            |
      | Final Exam      | 25%    | 80%            |
    And I export grades to the university's LMS
    Then I should see "Grades exported successfully"
    And students should see their final grades

    # Course Evaluation and Improvement
    Given the semester is complete
    When I analyze overall course performance
    And I review student feedback on assessments
    And I identify areas for improvement
      | Area                  | Issue                        | Improvement Plan              |
      | Question Clarity      | Some questions were ambiguous| Peer review all questions     |
      | Time Allocation       | Midterm was too rushed       | Increase time by 15 minutes   |
      | Feedback Quality      | Students want more examples   | Add worked examples to feedback|
    Then I should have data-driven insights for course improvement
    And I should be prepared for the next semester

    # Professional Development and Training
    Given I want to improve my assessment practices
    When I access professional development resources
    And I attend webinars on educational assessment
    And I participate in educator community forums
    Then I should continuously improve my teaching effectiveness
    And I should stay current with best practices

    # Research and Publication
    Given I want to contribute to educational research
    When I analyze anonymized student performance data
    And I identify trends in learning patterns
    And I prepare research findings for publication
    Then I should contribute to the broader educational community
    And I should advance the field of computer science education

    # Final Educator Journey Validation
    Then I should have successfully managed a complete semester of assessments
    And I should have comprehensive data on student learning
    And I should see improved student outcomes
    And I should have efficient assessment workflows
    And I should be prepared for continuous improvement
    And I should have contributed to student success and institutional goals
