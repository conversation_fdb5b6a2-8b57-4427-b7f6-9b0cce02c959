Feature: Pro Subscription User Journey
  As a professional user with a Pro subscription
  I want to access all premium features
  So that I can create comprehensive assessments and manage my team effectively

  @user-journey @pro-subscription @premium
  Scenario: Complete Pro Subscription User Experience
    # Registration and Immediate Upgrade
    Given I am on the registration page
    When I enter name "<PERSON>", email "<EMAIL>", and password "ProUser123!"
    And I accept the terms and conditions
    And I click the register button
    Then I should see a success message "Registration successful"
    
    When I verify my email and login
    And I am redirected to the dashboard
    And I click "Upgrade to Pro"
    Then I should see the pricing page

    # Pro Subscription Purchase
    When I select "Pro Monthly" plan
    And I enter payment details
      | Field           | Value                |
      | Card Number     | ****************     |
      | Expiry Date     | 12/25                |
      | CVC             | 123                  |
      | Name on Card    | Michael Chen         |
      | Billing Address | 123 Business St      |
      | City            | San Francisco        |
      | ZIP Code        | 94105                |
    And I click "Subscribe Now"
    Then I should see "Subscription successful"
    And I should see "Welcome to Pro!" message
    And I should be redirected to the Pro dashboard

    # Pro Dashboard Overview
    Given I am on the Pro dashboard
    Then I should see unlimited usage indicators
    And I should see "Questions: Unlimited"
    And I should see "Tests: Unlimited"
    And I should see "Team Members: Unlimited"
    And I should see "Advanced Analytics: Enabled"
    And I should see "Priority Support: Available"

    # Advanced Question Creation
    Given I am on the question creation page
    When I create a comprehensive question bank with multiple types
      | Type           | Count | Topics                    |
      | Multiple Choice| 15    | Programming, Algorithms   |
      | Code Input     | 10    | JavaScript, Python        |
      | Text Input     | 8     | System Design, Architecture|
      | Image Based    | 5     | UI/UX Design              |
      | Audio Based    | 3     | Language Skills           |
    Then I should see "43 questions created successfully"
    And I should see no usage limitations

    # Create Advanced Code Questions
    When I create a code input question
    And I enter question text "Write a function to reverse a linked list"
    And I select programming language "JavaScript"
    And I add test cases
      | Input                           | Expected Output              |
      | [1,2,3,4,5]                    | [5,4,3,2,1]                 |
      | [1]                            | [1]                          |
      | []                             | []                           |
    And I set time limit to "30 minutes"
    And I add detailed review guide
    Then I should see "Advanced code question created"

    # Bulk Question Import
    When I click "Bulk Import Questions"
    And I upload a JSON file with 50 questions
    Then I should see "50 questions imported successfully"
    And all questions should be properly categorized
    And I should see "93 total questions in question bank"

    # Create Multiple Test Suites
    Given I am on the test creation page
    When I create a comprehensive test suite
      | Test Name              | Questions | Duration | Difficulty |
      | Frontend Developer     | 25        | 60 min   | Medium     |
      | Backend Developer      | 30        | 90 min   | Hard       |
      | Full Stack Assessment  | 40        | 120 min  | Mixed      |
      | Junior Developer       | 15        | 45 min   | Easy       |
    Then I should see "4 tests created successfully"

    # Advanced Test Configuration
    Given I am editing the "Full Stack Assessment" test
    When I configure advanced settings
      | Setting                | Value                    |
      | Question Randomization | Enabled                  |
      | Time Limits per Section| Frontend: 45min, Backend: 75min |
      | Passing Score          | 75%                      |
      | Retake Policy          | 2 attempts allowed       |
      | Proctoring             | Enabled                  |
      | Auto-grading           | Enabled for MCQ/Code     |
      | Manual Review          | Required for Text Input  |
    And I set up question pools
      | Pool Name    | Questions | Random Selection |
      | Frontend     | 20        | 15 questions     |
      | Backend      | 25        | 20 questions     |
      | General      | 10        | 5 questions      |
    Then I should see "Advanced test configuration saved"

    # Team Management and Collaboration
    Given I am on the team management page
    When I invite team members
      | Name           | Email                    | Role      |
      | Sarah Wilson   | <EMAIL> | Admin     |
      | David Kim      | <EMAIL>    | Editor    |
      | Lisa Rodriguez | <EMAIL>| Reviewer |
      | Tom Anderson   | <EMAIL> | Viewer    |
    Then I should see "4 team invitations sent"
    
    When team members accept invitations
    Then I should see "4 active team members"
    And I should see role-based permissions working correctly

    # Collaborative Question Creation
    Given Sarah Wilson is logged in as an Editor
    When she creates questions and assigns them to test categories
    And David Kim reviews and approves the questions
    Then I should see collaborative workflow in action
    And I should see question approval notifications

    # Advanced Analytics and Reporting
    Given I have test data from multiple candidates
    When I go to the analytics dashboard
    Then I should see comprehensive analytics
      | Metric                    | Details                           |
      | Test Performance          | Average scores, completion rates  |
      | Question Analysis         | Difficulty analysis, discrimination|
      | Candidate Insights        | Time spent, answer patterns       |
      | Comparative Reports       | Benchmarking, percentiles         |
      | Trend Analysis            | Performance over time             |
    
    When I generate custom reports
    And I filter by date range, test type, and candidate groups
    Then I should see detailed custom analytics
    And I should be able to export reports in multiple formats

    # Candidate Management System
    Given I am on the candidate management page
    When I create candidate groups
      | Group Name        | Description                    |
      | Senior Developers | 5+ years experience           |
      | Junior Developers | 0-2 years experience          |
      | Interns          | University students           |
    And I assign tests to specific groups
    Then I should see organized candidate management

    # Automated Test Scheduling
    When I set up automated test campaigns
      | Campaign Name     | Target Group      | Schedule        |
      | Weekly Assessment | Senior Developers | Every Monday    |
      | Monthly Review    | All Developers    | 1st of month    |
      | Intern Evaluation | Interns          | Bi-weekly       |
    Then I should see "Automated campaigns configured"
    And I should receive scheduling confirmations

    # Integration with External Systems
    Given I am on the integrations page
    When I connect external systems
      | System    | Purpose                    |
      | Slack     | Notifications              |
      | JIRA      | Issue tracking             |
      | GitHub    | Code repository access     |
      | LinkedIn  | Candidate sourcing         |
    Then I should see "4 integrations active"
    And I should see data flowing between systems

    # Advanced Proctoring Features
    Given I am setting up a proctored test
    When I enable advanced proctoring features
      | Feature              | Setting    |
      | Camera Monitoring    | Enabled    |
      | Screen Recording     | Enabled    |
      | Browser Lockdown     | Enabled    |
      | Plagiarism Detection | Enabled    |
      | AI Behavior Analysis | Enabled    |
    Then I should see "Proctoring configured successfully"

    # White-label Customization
    Given I am on the branding settings page
    When I customize the platform appearance
      | Element           | Customization              |
      | Logo              | Company logo upload        |
      | Color Scheme      | Brand colors               |
      | Domain            | assessments.mycompany.com  |
      | Email Templates   | Branded email design       |
      | Certificate Design| Custom certificate layout  |
    Then I should see "White-label branding applied"
    And the platform should reflect my company branding

    # API Access and Automation
    Given I am on the API settings page
    When I generate API keys
    And I set up automated workflows
      | Workflow                  | Trigger                    |
      | Auto-invite candidates    | New hire in HR system      |
      | Send results to managers  | Test completion            |
      | Update candidate profiles | Score above threshold      |
    Then I should see "API automation configured"

    # Advanced Billing and Usage Management
    Given I am on the billing dashboard
    When I view detailed usage analytics
    Then I should see comprehensive usage data
      | Metric              | Current Usage    |
      | Tests Administered  | 1,247 this month |
      | Questions Created   | 93 total         |
      | Team Members        | 4 active         |
      | Storage Used        | 2.3 GB           |
      | API Calls           | 15,432 this month|
    
    When I set up usage alerts
    And I configure automatic scaling
    Then I should see "Usage monitoring configured"

    # Customer Success and Support
    When I access priority support
    Then I should see dedicated support channels
    And I should have access to a customer success manager
    When I schedule a strategy session
    Then I should see "Strategy session scheduled"

    # Performance Optimization
    Given I have large-scale test data
    When I run performance tests with 1000+ concurrent users
    Then the system should handle the load efficiently
    And response times should remain under 2 seconds
    And I should see real-time performance metrics

    # Data Export and Compliance
    When I export all platform data
    Then I should receive comprehensive data exports
    And I should see GDPR compliance features
    And I should have audit trail access

    # Subscription Management
    Given I am on the subscription management page
    When I view my Pro subscription details
    Then I should see subscription status, billing history, and usage
    When I update payment methods
    And I change billing frequency to annual
    Then I should see "Subscription updated successfully"
    And I should see the annual discount applied

    # Final Pro Experience Validation
    Then I should have access to all Pro features
    And I should see unlimited usage across all metrics
    And I should have a fully functional enterprise-grade assessment platform
    And I should see ROI through advanced features and team productivity
