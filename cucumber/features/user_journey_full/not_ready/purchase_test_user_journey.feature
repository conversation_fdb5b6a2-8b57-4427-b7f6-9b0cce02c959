Feature: Purchase Test User Journey
  As a user who wants to buy a pre-made test
  I want to browse, purchase, and use professional test templates
  So that I can quickly deploy high-quality assessments

  @user-journey @purchase-test @marketplace
  Scenario: Complete Test Purchase and Deployment Experience
    # Discover Test Marketplace
    Given I am logged in as "<EMAIL>"
    And I am on the dashboard
    When I click "Browse Test Marketplace"
    Then I should see the test marketplace
    And I should see featured test categories
      | Category              | Test Count |
      | Software Development  | 45         |
      | Data Science         | 23         |
      | Digital Marketing    | 18         |
      | Project Management   | 15         |
      | Customer Service     | 12         |
      | Sales & Business     | 20         |

    # Browse and Filter Tests
    When I click on "Software Development" category
    Then I should see available software development tests
    And I should see filter options
      | Filter Type    | Options                           |
      | Skill Level    | Beginner, Intermediate, Advanced  |
      | Technology     | JavaScript, Python, Java, React   |
      | Test Duration  | 30min, 60min, 90min, 120min      |
      | Price Range    | Free, $10-50, $50-100, $100+     |
      | Rating         | 4+ stars, 4.5+ stars             |

    When I filter by "JavaScript" and "Intermediate"
    Then I should see filtered test results
    And I should see "React Developer Assessment" test

    # View Test Details
    When I click on "React Developer Assessment"
    Then I should see the test details page
    And I should see comprehensive test information
      | Field              | Value                           |
      | Test Name          | React Developer Assessment      |
      | Creator            | TechSkills Pro                  |
      | Price              | $75                            |
      | Rating             | 4.8/5 (127 reviews)           |
      | Duration           | 90 minutes                     |
      | Questions          | 25 questions                   |
      | Skill Level        | Intermediate                   |
      | Technologies       | React, JavaScript, HTML, CSS   |
      | Last Updated       | 2 weeks ago                    |

    # Preview Test Content
    When I click "Preview Test"
    Then I should see a sample of test questions
    And I should see question types breakdown
      | Type              | Count | Percentage |
      | Multiple Choice   | 10    | 40%        |
      | Code Problems     | 8     | 32%        |
      | Text Input        | 5     | 20%        |
      | Practical Tasks   | 2     | 8%         |

    When I view sample questions
    Then I should see high-quality, relevant questions
    And I should see detailed explanations for answers
    And I should see code examples and best practices

    # Read Reviews and Ratings
    When I scroll to the reviews section
    Then I should see detailed user reviews
      | Reviewer           | Rating | Comment                                    |
      | HR Manager         | 5/5    | "Excellent questions, very comprehensive"  |
      | Tech Lead          | 4/5    | "Good coverage of React concepts"          |
      | Startup Founder    | 5/5    | "Helped us hire 3 great developers"       |
    And I should see review statistics
    And I should see "95% would recommend this test"

    # Compare with Similar Tests
    When I click "Compare Similar Tests"
    Then I should see a comparison table
      | Test Name              | Price | Questions | Duration | Rating |
      | React Developer Pro    | $75   | 25        | 90min    | 4.8/5  |
      | Frontend Assessment    | $60   | 20        | 75min    | 4.6/5  |
      | React Skills Test      | $45   | 15        | 60min    | 4.4/5  |
    And I should see feature comparisons

    # Add to Cart and Purchase
    When I click "Add to Cart"
    Then I should see "Test added to cart"
    And I should see the cart icon updated
    When I click on the cart
    Then I should see my cart contents
      | Test Name              | Price | License Type |
      | React Developer Pro    | $75   | Single Use   |

    When I click "Proceed to Checkout"
    Then I should see the checkout page
    And I should see license options
      | License Type    | Price | Description                    |
      | Single Use      | $75   | Use once, up to 50 candidates |
      | Multi-Use       | $150  | Use 5 times, unlimited candidates |
      | Enterprise      | $300  | Unlimited use, white-label     |

    # Select License and Payment
    When I select "Multi-Use" license
    And I see the updated total of $150
    And I enter payment information
      | Field           | Value                |
      | Card Number     | ****************     |
      | Expiry Date     | 12/25                |
      | CVC             | 123                  |
      | Name on Card    | John Buyer           |
      | Billing Address | 456 Corporate Ave    |
      | City            | New York             |
      | ZIP Code        | 10001                |
    And I apply coupon code "NEWUSER10"
    Then I should see "10% discount applied"
    And I should see the final total of $135

    When I click "Complete Purchase"
    Then I should see "Purchase successful!"
    And I should receive a purchase confirmation email
    And I should see "Test added to your library"

    # Access Purchased Test
    Given the purchase is complete
    When I go to "My Test Library"
    Then I should see "React Developer Assessment" in my library
    And I should see license information
      | Field           | Value                    |
      | License Type    | Multi-Use               |
      | Uses Remaining  | 5                       |
      | Expires         | Never                   |
      | Purchase Date   | Today's date            |

    # Customize Purchased Test
    When I click "Customize Test"
    Then I should see customization options
      | Option              | Available |
      | Edit Questions      | ✓         |
      | Add Questions       | ✓         |
      | Remove Questions    | ✓         |
      | Modify Settings     | ✓         |
      | Rebrand Test        | ✓         |
      | Change Duration     | ✓         |

    When I customize the test
    And I change the test name to "Senior React Developer Assessment"
    And I add 5 additional advanced questions from my question bank
    And I increase duration to 120 minutes
    And I update the company branding
    Then I should see "Test customized successfully"
    And I should see the updated test in my library

    # Deploy Purchased Test
    When I click "Deploy Test"
    Then I should see deployment options
      | Option              | Description                    |
      | Public Link         | Generate shareable public link |
      | Invitation Codes    | Create invitation codes        |
      | Email Invitations   | Send direct email invitations  |
      | Embed Code          | Embed in website/portal        |
      | API Integration     | Integrate with ATS/HRIS        |

    When I select "Email Invitations"
    And I upload a candidate list CSV file
      | Name           | Email                    | Role Applied For    |
      | Alice Johnson  | <EMAIL>        | Senior React Dev    |
      | Bob Smith      | <EMAIL>      | React Developer     |
      | Carol Davis    | <EMAIL>    | Frontend Developer  |
    And I customize the invitation email template
    And I schedule invitations to be sent tomorrow at 9 AM
    Then I should see "3 invitations scheduled"

    # Monitor Test Usage
    Given candidates start taking the test
    When I go to the test analytics dashboard
    Then I should see real-time test statistics
      | Metric              | Value |
      | Invitations Sent    | 3     |
      | Tests Started       | 2     |
      | Tests Completed     | 1     |
      | Average Score       | 78%   |
      | Average Duration    | 95min |
      | Completion Rate     | 50%   |

    # Candidate Results and Evaluation
    When candidates complete the test
    Then I should see detailed candidate results
      | Candidate      | Score | Status | Completion Time |
      | Alice Johnson  | 92%   | Passed | 87 minutes     |
      | Bob Smith      | 74%   | Passed | 103 minutes    |
      | Carol Davis    | 68%   | Failed | 89 minutes     |

    When I click on Alice Johnson's results
    Then I should see comprehensive performance analysis
      | Section           | Score | Strengths              | Areas for Improvement |
      | React Fundamentals| 95%   | Excellent understanding| None                  |
      | State Management  | 90%   | Good Redux knowledge   | Context API           |
      | Performance       | 88%   | Optimization skills    | Memory management     |
      | Testing           | 92%   | Jest expertise         | E2E testing           |

    # Generate Hiring Reports
    When I click "Generate Hiring Report"
    Then I should see a comprehensive hiring report
    And I should see candidate rankings
    And I should see skill gap analysis
    And I should see hiring recommendations
    When I export the report as PDF
    Then I should receive a professional hiring report

    # Test Library Management
    Given I have multiple purchased tests
    When I go to my test library
    Then I should see all purchased tests organized
    And I should see usage statistics for each test
    And I should see license status and expiration dates
    When I search for tests by technology or role
    Then I should see filtered results

    # Bulk Purchase for Team
    Given I want to purchase tests for my entire team
    When I select multiple tests for bulk purchase
      | Test Name                  | Price | Quantity |
      | React Developer Assessment | $75   | 5        |
      | Node.js Backend Test       | $80   | 3        |
      | Full Stack Evaluation      | $120  | 2        |
    And I apply team discount code "TEAM20"
    Then I should see "20% team discount applied"
    And I should see bulk pricing benefits

    # Subscription for Unlimited Access
    When I see the option for "Test Marketplace Subscription"
    And I click "Learn More"
    Then I should see subscription benefits
      | Benefit                    | Included |
      | Unlimited Test Downloads   | ✓        |
      | New Tests Added Monthly    | ✓        |
      | Premium Support           | ✓        |
      | Advanced Analytics        | ✓        |
      | White-label Rights        | ✓        |
      | Custom Test Requests      | ✓        |

    # Seller Dashboard (if I want to sell tests)
    When I click "Become a Test Creator"
    Then I should see the seller onboarding process
    And I should see revenue sharing information
    And I should see quality guidelines
    When I submit my test for marketplace approval
    Then I should see "Test submitted for review"

    # Customer Support and Satisfaction
    When I need help with a purchased test
    And I contact customer support
    Then I should receive prompt assistance
    And I should see my purchase history
    And I should be able to request refunds if unsatisfied

    # Final Purchase Journey Validation
    Then I should have successfully purchased and deployed a professional test
    And I should have comprehensive candidate evaluation data
    And I should see clear ROI from the test purchase
    And I should have access to ongoing support and updates
    And I should be able to make informed hiring decisions based on test results
