Feature: Public Test Taker User <PERSON>: Take a public test as a guest
        Given I am a test taker
        And I want to take the test named "Sample Test"
        When I go to the test join form for "Sample Test"
        And I enter my name "<PERSON>" and email "<EMAIL>"
        And I submit the join form
        Then I should see the test landing page for "Sample Test"
        When I start the test session
        And I answer all questions
        And I submit the test
        Then I should see my test results for "Sample Test"
        Then I should see email confirmation
        Then I should see certificate

    Scenario: Take a public test as a logged-in user
        Given I am logged in as a user "<EMAIL>"
        And I want to take the test named "Sample Test"
        When I go to the test landing page for "Sample Test"
        And I start the test session
        And I answer all questions
        And I submit the test
        Then I should see my test results for "Sample Test"
        Then I should see my test session in my dashboard /my-tests
