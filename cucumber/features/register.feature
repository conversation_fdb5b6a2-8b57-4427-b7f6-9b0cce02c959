Feature: User Registration

  Scenario: Successful user registration
    Given I am on the registration page
    When I enter name "<PERSON>", email "<EMAIL>", and password "password123"
    And I click the register button
    Then I should see a register success message "Registration successful"
    And I should be redirected to the login page

  Scenario: Registration fails due to duplicate email
    Given I am on the registration page
    When I enter name "<PERSON>", email "<EMAIL>", and password "password123"
    And I click the register button
    Then I should see an register error message "Registration failed. Please try again."

  Scenario: Registration fails due to missing fields
    Given I am on the registration page
    When I enter name "", email "<EMAIL>", and password "password123"
    And I click the register button
    Then I should see an register error message "Name is required"

  Scenario: Registration fails due to invalid email format
    Given I am on the registration page
    When I enter name "<PERSON> User", email "invalid-email", and password "password123"
    And I click the register button
    Then I should see an register error message "Invalid email format"
