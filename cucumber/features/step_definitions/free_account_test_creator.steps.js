const { Given, When, Then } = require("@cucumber/cucumber")
const assert = require("assert")

const baseUrl = process.env.BASE_URL || "http://localhost:3000"

Given("the test environment is set up", function () {})

Given("I am on the test creation page", async function () {
  await this.page.goto(`${baseUrl}/dashboard/test-builder/new`)
  await this.page.waitForSelector('[data-testid="test-creation-page"]')
})

Given("I am in the test editor for {string}", async function (testName) {
  await this.page.goto(`${baseUrl}/dashboard/test-builder/edit`)
  await this.page.waitForSelector('[data-testid="test-editor-page"]')

  const testTitle = await this.page
    .locator('[data-testid="test-title"]')
    .textContent()
  assert.ok(
    testTitle.includes(testName),
    `Expected to be editing test "${testName}", but found "${testTitle}"`,
  )
})

Given("I am back on the dashboard as the test creator", async function () {
  if (this.testPage) {
    await this.testPage.close()
    this.testPage = null
  }

  await this.page.goto(`${baseUrl}/dashboard`)
  await this.page.waitForSelector('[data-testid="dashboard"]')
})

Given("I am on the collaboration settings page", async function () {
  await this.page.goto(`${baseUrl}/dashboard/settings/collaboration`)
  await this.page.waitForSelector('[data-testid="collaboration-settings-page"]')
})

Given("I am on the usage dashboard", async function () {
  await this.page.goto(`${baseUrl}/dashboard/usage`)
  await this.page.waitForSelector('[data-testid="usage-dashboard"]')
})

Given("I am using a mobile device", async function () {
  await this.page.setViewportSize({ width: 375, height: 667 })
})

// Question Creation Steps
When("I click {string}", async function (buttonText) {
  const buttonSelector = getButtonSelector(buttonText)
  await this.page.click(buttonSelector)
})

When("I select question type {string}", async function (questionType) {
  await this.page.selectOption(
    '[data-testid="question-type-select"]',
    questionType,
  )
})

When("I enter question text {string}", async function (questionText) {
  await this.page.fill('[data-testid="question-text-input"]', questionText)
  this.currentQuestionText = questionText
})

When("I add option {string} as correct answer", async function (optionText) {
  await this.page.click('[data-testid="add-option-button"]')
  const optionInputs = await this.page
    .locator('[data-testid^="option-text-"]')
    .count()
  const lastOptionIndex = optionInputs - 1
  await this.page.fill(
    `[data-testid="option-text-${lastOptionIndex}"]`,
    optionText,
  )
  await this.page.check(`[data-testid="option-correct-${lastOptionIndex}"]`)
})

When("I add option {string} as incorrect answer", async function (optionText) {
  await this.page.click('[data-testid="add-option-button"]')
  const optionInputs = await this.page
    .locator('[data-testid^="option-text-"]')
    .count()
  const lastOptionIndex = optionInputs - 1
  await this.page.fill(
    `[data-testid="option-text-${lastOptionIndex}"]`,
    optionText,
  )
})

When("I add tags {string}", async function (tags) {
  const tagArray = tags.split(",").map((tag) => tag.trim())
  for (const tag of tagArray) {
    await this.page.fill('[data-testid="tags-input"]', tag)
    await this.page.press('[data-testid="tags-input"]', "Enter")
  }
})

When("I click the save button", async function () {
  await this.page.click('[data-testid="save-question-button"]')
})

// Test Creation Steps
When("I enter test name {string}", async function (testName) {
  await this.page.fill('[data-testid="test-name-input"]', testName)
  this.currentTestName = testName
})

When("I enter test description {string}", async function (description) {
  await this.page.fill('[data-testid="test-description-input"]', description)
})

When("I set test duration to {string}", async function (duration) {
  await this.page.fill('[data-testid="test-duration-input"]', duration)
})

When("I select difficulty {string}", async function (difficulty) {
  await this.page.selectOption(
    '[data-testid="test-difficulty-select"]',
    difficulty,
  )
})

// Question Management Steps
When("I create {int} more basic questions", async function (questionCount) {
  for (let i = 0; i < questionCount; i++) {
    await this.page.click('[data-testid="create-question-button"]')
    await this.page.selectOption(
      '[data-testid="question-type-select"]',
      "multiple choice",
    )
    await this.page.fill(
      '[data-testid="question-text-input"]',
      `Sample question ${i + 2}?`,
    )

    await this.page.click('[data-testid="add-option-button"]')
    await this.page.fill('[data-testid="option-text-0"]', "Correct answer")
    await this.page.check('[data-testid="option-correct-0"]')

    await this.page.click('[data-testid="add-option-button"]')
    await this.page.fill('[data-testid="option-text-1"]', "Wrong answer")

    await this.page.click('[data-testid="save-question-button"]')
    await this.page.waitForSelector('[data-testid="success-message"]')
    await this.page.goto(`${baseUrl}/dashboard/question-banks`)
  }
})

When("I try to create another question", async function () {
  await this.page.click('[data-testid="create-question-button"]')
})

When("I search for questions with tag {string}", async function (tag) {
  await this.page.fill('[data-testid="question-search-input"]', tag)
  await this.page.press('[data-testid="question-search-input"]', "Enter")
})

When("I add the question {string} to the test", async function (questionText) {
  const questionRow = this.page.locator(
    `[data-testid="question-row"]:has-text("${questionText}")`,
  )
  await questionRow.locator('[data-testid="add-to-test-button"]').click()
})

When(
  "I add {int} more geography questions to the test",
  async function (questionCount) {
    const questionRows = this.page.locator('[data-testid="question-row"]')
    const totalQuestions = await questionRows.count()
    for (let i = 1; i < Math.min(questionCount + 1, totalQuestions); i++) {
      await questionRows
        .nth(i)
        .locator('[data-testid="add-to-test-button"]')
        .click()
    }
  },
)

When("I set access to {string}", async function (accessLevel) {
  await this.page.selectOption(
    '[data-testid="test-access-select"]',
    accessLevel,
  )
})

// Test Configuration Steps
When("I enable {string}", async function (setting) {
  const settingMap = {
    "Show results immediately": '[data-testid="show-results-toggle"]',
  }
  const selector =
    settingMap[setting] ||
    `[data-testid="${setting.toLowerCase().replace(/\s+/g, "-")}-toggle"]`
  await this.page.check(selector)
})

When("I set passing score to {string}", async function (score) {
  await this.page.fill('[data-testid="passing-score-input"]', score)
})

When("I generate an invitation code", async function () {
  await this.page.click('[data-testid="generate-invitation-code-button"]')
})

// Test Taking Steps
When("I open the public test link in a new tab", async function () {
  const testLink = await this.page
    .locator('[data-testid="public-test-link"]')
    .getAttribute("href")
  this.testPage = await this.context.newPage()
  await this.testPage.goto(testLink)
})

When("I enter name {string} and email {string}", async function (name, email) {
  await this.testPage.fill('[data-testid="guest-name-input"]', name)
  await this.testPage.fill('[data-testid="guest-email-input"]', email)
})

When("I answer all questions correctly", async function () {
  const questions = await this.testPage
    .locator('[data-testid^="question-"]')
    .count()
  for (let i = 0; i < questions; i++) {
    await this.testPage.click(
      `[data-testid="question-${i}"] [data-testid="option-0"]`,
    )
    if (i < questions - 1) {
      await this.testPage.click('[data-testid="next-question-button"]')
    }
  }
})

When("I copy the test link", async function () {
  await this.page.click('[data-testid="copy-test-link-button"]')
})

When("I go to the test analytics page for {string}", async function (testName) {
  await this.page.goto(
    `${baseUrl}/dashboard/tests/analytics?test=${encodeURIComponent(testName)}`,
  )
  await this.page.waitForSelector('[data-testid="analytics-page"]')
})

When("I try to invite a team member", async function () {
  await this.page.click('[data-testid="invite-team-member-button"]')
})

When("I try to create a second test", async function () {
  await this.page.click('[data-testid="create-test-button"]')
})

When("I access the dashboard", async function () {
  await this.page.goto(`${baseUrl}/dashboard`)
  await this.page.waitForSelector('[data-testid="dashboard"]')
})

When("I take a test on mobile", async function () {
  const testInterface = this.page.locator(
    '[data-testid="mobile-test-interface"]',
  )
  const isVisible = await testInterface.isVisible()
  assert.ok(isVisible, "Expected mobile test interface to be available")
})

Then("I should see a success message {string}", async function (message) {
  const successMessage = this.page.locator('[data-testid="success-message"]')
  await successMessage.waitFor({ state: "visible" })
  const text = await successMessage.textContent()
  assert.ok(
    text.includes(message),
    `Expected success message to contain "${message}", but got "${text}"`,
  )
})

Then("I should see {string}", async function (expectedText) {
  const textLocator = this.page.locator(`text=${expectedText}`)
  const isVisible = await textLocator.isVisible()
  assert.ok(isVisible, `Expected text "${expectedText}" to be visible`)
})

Then(
  "I should see {string} in the free plan counter",
  async function (counterText) {
    const counterLocator = this.page.locator(
      '[data-testid="free-plan-counter"]',
    )
    const counterContent = await counterLocator.textContent()
    assert.ok(
      counterContent.includes(counterText),
      `Expected counter to show "${counterText}", but found "${counterContent}"`,
    )
  },
)

Then("I should be redirected to the test editor", async function () {
  await this.page.waitForURL("**/test-builder/edit")
  const url = this.page.url()
  assert.ok(
    url.includes("/test-builder/edit"),
    `Expected to be redirected to test editor, but URL is ${url}`,
  )
})

Then(
  "I should see {string} questions added to test",
  async function (questionCount) {
    const questionsAddedLocator = this.page.locator(
      '[data-testid="questions-added-count"]',
    )
    const questionsText = await questionsAddedLocator.textContent()
    assert.ok(
      questionsText.includes(questionCount),
      `Expected to see "${questionCount} questions added", but found "${questionsText}"`,
    )
  },
)

Then(
  "the test status should change to {string}",
  async function (expectedStatus) {
    const statusLocator = this.page.locator('[data-testid="test-status"]')
    const actualStatus = await statusLocator.textContent()
    assert.strictEqual(
      actualStatus.trim(),
      expectedStatus,
      `Expected test status to be "${expectedStatus}", but found "${actualStatus}"`,
    )
  },
)

Then("I should see the public test link", async function () {
  const linkLocator = this.page.locator('[data-testid="public-test-link"]')
  const isVisible = await linkLocator.isVisible()
  assert.ok(isVisible, "Expected public test link to be visible")
})

Then("I should see the test instructions", async function () {
  const instructionsLocator = this.testPage.locator(
    '[data-testid="test-instructions"]',
  )
  const isVisible = await instructionsLocator.isVisible()
  assert.ok(isVisible, "Expected test instructions to be visible")
})

Then("I should see {string} test", async function (testName) {
  const page = this.testPage || this.page
  const testTitleLocator = page.locator('[data-testid="test-title"]')
  const titleText = await testTitleLocator.textContent()
  assert.ok(
    titleText.includes(testName),
    `Expected to see test "${testName}", but found "${titleText}"`,
  )
})

Then("I should see my test results", async function () {
  const resultsLocator = this.testPage.locator('[data-testid="test-results"]')
  const isVisible = await resultsLocator.isVisible()
  assert.ok(isVisible, "Expected test results to be visible")
})

Then("I should be redirected to the home page", async function () {
  await this.page.waitForURL(baseUrl)
})

Then("all my data should be preserved", async function () {
  const dashboard = this.page.locator('[data-testid="dashboard"]')
  const isVisible = await dashboard.isVisible()
  assert.ok(isVisible, "Expected user data to be preserved after re-login")
})

Then("I should see my created test {string}", async function (testName) {
  const testLocator = this.page.locator(
    `[data-testid="test-item"]:has-text("${testName}")`,
  )
  const isVisible = await testLocator.isVisible()
  assert.ok(isVisible, `Expected to see created test "${testName}"`)
})

// Additional Assertion Steps
Then("I should see the test as students would see it", async function () {
  const previewLocator = this.page.locator('[data-testid="test-preview"]')
  const isVisible = await previewLocator.isVisible()
  assert.ok(isVisible, "Expected test preview to be visible")
})

Then("I should see all {int} questions", async function (questionCount) {
  const questionsLocator = this.page.locator(
    '[data-testid^="preview-question-"]',
  )
  const actualCount = await questionsLocator.count()
  assert.strictEqual(
    actualCount,
    questionCount,
    `Expected ${questionCount} questions, but found ${actualCount}`,
  )
})

Then("I should see the test timer", async function () {
  const timerLocator = this.page.locator('[data-testid="test-timer"]')
  const isVisible = await timerLocator.isVisible()
  assert.ok(isVisible, "Expected test timer to be visible")
})

Then("I should see the generated invitation code", async function () {
  const codeLocator = this.page.locator('[data-testid="invitation-code"]')
  const isVisible = await codeLocator.isVisible()
  assert.ok(isVisible, "Expected invitation code to be visible")
})

Then("I should see sharing options", async function () {
  const sharingLocator = this.page.locator('[data-testid="sharing-options"]')
  const isVisible = await sharingLocator.isVisible()
  assert.ok(isVisible, "Expected sharing options to be visible")
})

Then("I should see the invitation code", async function () {
  const codeLocator = this.page.locator(
    '[data-testid="invitation-code-display"]',
  )
  const isVisible = await codeLocator.isVisible()
  assert.ok(isVisible, "Expected invitation code to be displayed")
})

Then("I should see the public test URL", async function () {
  const urlLocator = this.page.locator('[data-testid="public-test-url"]')
  const isVisible = await urlLocator.isVisible()
  assert.ok(isVisible, "Expected public test URL to be visible")
})

Then("I should see basic analytics", async function () {
  const analyticsLocator = this.page.locator('[data-testid="basic-analytics"]')
  const isVisible = await analyticsLocator.isVisible()
  assert.ok(isVisible, "Expected basic analytics to be visible")
})

Then("I should see {string} test attempt", async function (attemptCount) {
  const attemptLocator = this.page.locator('[data-testid="test-attempts"]')
  const attemptText = await attemptLocator.textContent()
  assert.ok(
    attemptText.includes(attemptCount),
    `Expected to see "${attemptCount} test attempt", but found "${attemptText}"`,
  )
})

Then("I should see {string} average score", async function (averageScore) {
  const scoreLocator = this.page.locator('[data-testid="average-score"]')
  const scoreText = await scoreLocator.textContent()
  assert.ok(
    scoreText.includes(averageScore),
    `Expected to see "${averageScore} average score", but found "${scoreText}"`,
  )
})

Then("I should see limited analytics features", async function () {
  const limitedLocator = this.page.locator('[data-testid="limited-analytics"]')
  const isVisible = await limitedLocator.isVisible()
  assert.ok(isVisible, "Expected limited analytics indicator to be visible")
})

Then("I should see the pricing page", async function () {
  await this.page.waitForSelector('[data-testid="pricing-page"]')
  const url = this.page.url()
  assert.ok(
    url.includes("/pricing"),
    `Expected to be on pricing page, but URL is ${url}`,
  )
})

Then("I should see Pro plan benefits", async function () {
  const benefitsLocator = this.page.locator('[data-testid="pro-plan-benefits"]')
  const isVisible = await benefitsLocator.isVisible()
  assert.ok(isVisible, "Expected Pro plan benefits to be visible")
})

Then("I should see my current plan usage", async function () {
  const usageLocator = this.page.locator('[data-testid="plan-usage"]')
  const isVisible = await usageLocator.isVisible()
  assert.ok(isVisible, "Expected plan usage to be visible")
})

Then("I should see upgrade recommendations", async function () {
  const recommendationsLocator = this.page.locator(
    '[data-testid="upgrade-recommendations"]',
  )
  const isVisible = await recommendationsLocator.isVisible()
  assert.ok(isVisible, "Expected upgrade recommendations to be visible")
})

Then("the interface should be mobile-responsive", async function () {
  const mobileLayoutLocator = this.page.locator('[data-testid="mobile-layout"]')
  const isVisible = await mobileLayoutLocator.isVisible()
  assert.ok(isVisible, "Expected mobile-responsive layout to be active")
})

Then("all features should work on mobile", async function () {
  const menuButton = this.page.locator('[data-testid="mobile-menu-button"]')
  if (await menuButton.isVisible()) {
    await menuButton.click()
    const mobileMenu = this.page.locator('[data-testid="mobile-menu"]')
    const isVisible = await mobileMenu.isVisible()
    assert.ok(isVisible, "Expected mobile menu to be functional")
  }
})

Then("the test interface should be touch-friendly", async function () {
  const touchInterface = this.page.locator(
    '[data-testid="touch-friendly-interface"]',
  )
  const isVisible = await touchInterface.isVisible()
  assert.ok(isVisible, "Expected touch-friendly interface to be active")
})

Then("I should be able to complete the test successfully", async function () {
  const completionIndicator = this.page.locator(
    '[data-testid="test-completion-possible"]',
  )
  const isVisible = await completionIndicator.isVisible()
  assert.ok(isVisible, "Expected test completion to be possible on mobile")
})

Then(
  "I should have successfully completed the free account journey",
  async function () {
    const dashboardLocator = this.page.locator('[data-testid="dashboard"]')
    const isVisible = await dashboardLocator.isVisible()
    assert.ok(isVisible, "Expected to be on dashboard after completing journey")
  },
)

Then("I should understand the platform's value proposition", async function () {
  const url = this.page.url()
  assert.ok(
    url.includes("/dashboard"),
    "Expected to be on dashboard showing platform value",
  )
})

Then("I should be aware of Pro plan benefits", async function () {
  const upgradePromptLocator = this.page.locator(
    '[data-testid="upgrade-prompt"]',
  )
  if ((await upgradePromptLocator.count()) > 0) {
    const isVisible = await upgradePromptLocator.isVisible()
    assert.ok(isVisible, "Expected upgrade prompt to be visible when present")
  }
})

Then("I should have a functional test ready for sharing", async function () {
  const activeTestLocator = this.page.locator(
    '[data-testid="test-item"][data-status="ACTIVE"]',
  )
  const testExists = (await activeTestLocator.count()) > 0
  assert.ok(
    testExists,
    "Expected to have at least one active test ready for sharing",
  )
})

// Helper function to get button selectors
function getButtonSelector(buttonText) {
  const buttonMap = {
    "Create Question": '[data-testid="create-question-button"]',
    "Create Test": '[data-testid="create-test-button"]',
    "Save Test": '[data-testid="save-test-button"]',
    "Test Settings": '[data-testid="test-settings-button"]',
    "Save Settings": '[data-testid="save-settings-button"]',
    "Publish Test": '[data-testid="publish-test-button"]',
    "Preview Test": '[data-testid="preview-test-button"]',
    "Share Test": '[data-testid="share-test-button"]',
    "Start Test": '[data-testid="start-test-button"]',
    "Begin Test": '[data-testid="begin-test-button"]',
    "Submit Test": '[data-testid="submit-test-button"]',
    "Upgrade to Pro": '[data-testid="upgrade-to-pro-button"]',
  }
  return buttonMap[buttonText] || `button:has-text("${buttonText}")`
}
