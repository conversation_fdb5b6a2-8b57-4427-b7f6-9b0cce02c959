const { Given, When, Then } = require("@cucumber/cucumber")
const assert = require("assert")

const baseUrl = process.env.BASE_URL || "http://localhost:3000"

Given("I am on the registration page", async function () {
  await this.page.goto(`${baseUrl}/auth/register`)
  await this.page.waitForSelector('[data-testid="registration-page"]')
})

When(
  "I enter name {string}, email {string}, and password {string}",
  async function (name, email, password) {
    await this.page.fill('[data-testid="name-input"]', name)
    await this.page.fill('[data-testid="email-input"]', email)
    await this.page.fill('[data-testid="password-input"]', password)
  },
)

When("I accept the terms and conditions", async function () {
  await this.page.check('[data-testid="terms-checkbox"]')
})

When("I click the register button", async function () {
  await this.page.click('[data-testid="register-button"]')
})

When(
  "I register again with {string}, {string}, and {string}",
  async function (name, email, password) {
    await this.page.goto(`${baseUrl}/auth/register`)
    await this.page.fill('[data-testid="name-input"]', name)
    await this.page.fill('[data-testid="email-input"]', email)
    await this.page.fill('[data-testid="password-input"]', password)
    await this.page.check('[data-testid="terms-checkbox"]')
    await this.page.click('[data-testid="register-button"]')
  },
)

Then(
  "I should see a register success message {string}",
  async function (expectedMessage) {
    const successMessageLocator = await this.page.locator(
      "text=" + expectedMessage,
    )
    const isVisible = await successMessageLocator.isVisible()
    assert.ok(
      isVisible,
      `Expected success message "${expectedMessage}" to be visible`,
    )
  },
)

Then(
  "I should see an register error message {string}",
  async function (expectedMessage) {
    const errorMessageLocator = await this.page.locator(
      "text=" + expectedMessage,
    )
    const isVisible = await errorMessageLocator.isVisible()
    assert.ok(
      isVisible,
      `Expected error message "${expectedMessage}" to be visible`,
    )
  },
)
