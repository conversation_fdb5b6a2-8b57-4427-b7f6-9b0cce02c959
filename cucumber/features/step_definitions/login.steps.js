const { Given, When, Then } = require("@cucumber/cucumber")
const assert = require("assert")

const baseUrl = process.env.BASE_URL || "http://localhost:3000"

Given("I am on the login page", async function () {
  await this.page.goto(`${baseUrl}/auth/login`)
  await this.page.waitForSelector('[data-testid="login-page"]')
})

When("I enter {string} and {string}", async function (email, password) {
  await this.page.fill('[data-testid="email-input"]', email)
  await this.page.fill('[data-testid="password-input"]', password)
})

When("I click the login button", async function () {
  await this.page.click('[data-testid="login-button"]')
})

When(
  "I login again with {string} and {string}",
  async function (email, password) {
    await this.page.goto(`${baseUrl}/auth/login`)
    await this.page.fill('[data-testid="email-input"]', email)
    await this.page.fill('[data-testid="password-input"]', password)
    await this.page.click('[data-testid="login-button"]')
  },
)

When("I logout from the application", async function () {
  await this.page.click('[data-testid="user-menu-button"]')
  await this.page.click('[data-testid="logout-button"]')
})

Then(
  "I should see an login error message {string}",
  async function (expectedMessage) {
    const errorMessageLocator = await this.page.locator(
      "text=" + expectedMessage,
    )
    const isVisible = await errorMessageLocator.isVisible()
    assert.ok(
      isVisible,
      `Expected error message "${expectedMessage}" to be visible`,
    )
  },
)

Then("I should be redirected to the login page", async function () {
  await this.page.waitForURL(`${baseUrl}/auth/login`)
  const url = this.page.url()
  assert.strictEqual(url, `${baseUrl}/auth/login`)
})
