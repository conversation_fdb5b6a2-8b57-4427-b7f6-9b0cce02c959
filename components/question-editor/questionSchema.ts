import { z } from "zod"

const datePreprocessor = z.preprocess((arg) => {
  if (typeof arg === "string" || arg instanceof Date) {
    const d = new Date(arg)
    if (!isNaN(d.getTime())) return d
  }
  return undefined
}, z.date())

const baseQuestionSchema = z.object({
  id: z.string(),
  type: z.enum([
    "multipleChoice",
    "singleChoice",
    "textInput",
    "codeInput",
    "imageBased",
    "audioBased",
    "audioAnswer",
    "voiceInput",
    "fileUpload",
  ]),
  question: z.string(),
  internal: z.record(z.object({}).passthrough()).optional(), // record<string, object>
  createdAt: datePreprocessor.optional(),
  updatedAt: datePreprocessor.optional(),
  tags: z.array(z.string()).optional(),
})

// Option schemas
const optionTextSchema = z.object({
  id: z.string(),
  text: z.string(),
})

const optionAudioSchema = z.object({
  id: z.string(),
  audio: z.string(),
})

// Question type specific schemas
const multipleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("multipleChoice"),
  options: z.array(optionTextSchema),
  correctAnswers: z.array(z.string()),
})

const singleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("singleChoice"),
  options: z.array(optionTextSchema),
  correctAnswer: z.string(),
})

const textInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("textInput"),
  reviewGuide: z.string(),
  caseSensitive: z.boolean(),
})

const testCaseSchema = z.object({
  input: z.array(z.any()), // input is an array of any type
  expectedOutput: z.any(), // expected output can be any type (number, string, object, etc.)
})

const codeInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("codeInput"),
  reviewGuide: z.string(),
  language: z.string(),
  testCases: z.array(testCaseSchema),
})

const imageBasedQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("imageBased"),
  image: z.string(),
  options: z.array(optionTextSchema),
  correctAnswers: z.array(z.string()),
})

const audioBasedQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("audioBased"),
  audio: z.string(),
  options: z.array(optionTextSchema),
  correctAnswers: z.array(z.string()),
})

const audioAnswerQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("audioAnswer"),
  options: z.array(optionAudioSchema),
  reviewGuide: z.string(),
})

const voiceInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("voiceInput"),
  reviewGuide: z.string(),
  maxDuration: z.number().optional(),
  correctPhrases: z.array(z.string()).optional(),
})

const fileUploadQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("fileUpload"),
  reviewGuide: z.string(),
  allowedFileTypes: z.array(z.string()).optional(),
  maxFileSize: z.number().optional(),
})

// The full union schema for all question types
export const questionSchema = z.discriminatedUnion("type", [
  multipleChoiceQuestionSchema,
  singleChoiceQuestionSchema,
  textInputQuestionSchema,
  codeInputQuestionSchema,
  imageBasedQuestionSchema,
  audioBasedQuestionSchema,
  audioAnswerQuestionSchema,
  voiceInputQuestionSchema,
  fileUploadQuestionSchema,
])

// Optional: schema for a single question answer
export const questionAnswerSchema = z.object({
  id: z.string(),
  questionId: z.string(),
  answer: z.array(z.string()),
  isCorrect: z.boolean().optional(),
  reviewedAt: z.string().optional(),
})

// Optional: schema for filters
export const questionFiltersSchema = z.object({
  difficulty: z.enum(["EASY", "MEDIUM", "HARD"]).optional(),
  tags: z.array(z.string()).optional(),
  createdBy: z.string().optional(),
})
