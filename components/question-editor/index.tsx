"use client"

import React, { useEffect, useRef, useState } from "react"
import { X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { useLocalization } from "@/src/localization/functions/client"
import en from "@/src/app/dashboard/question-editor/locales/en.json"
import { Question } from "@/src/lib/repositories/questions/interface"
import { JsonEditor } from "./jsonEditor"
import { DynamicAnswerEditor } from "./DynamicAnswerEditor"

interface QuestionEditorProps {
  question: Question
  pluginRegistry: Record<string, React.FC<any>>
  onChange: (updatedQuestion: Partial<any>) => void
  onUpdate: () => void
}

export function QuestionEditor({
  question,
  pluginRegistry,
  onChange,
  onUpdate,
}: QuestionEditorProps) {
  const [jsonMode, setJsonMode] = useState(false)
  const [tagInput, setTagInput] = useState("")

  return (
    <div className="space-y-6">
      <Header
        jsonMode={jsonMode}
        setJsonMode={setJsonMode}
        onUpdate={onUpdate}
      />
      <div className="space-y-5 w-full">
        {jsonMode ? (
          <JsonEditor question={question} onChange={onChange} />
        ) : (
          <>
            <div className="flex gap-2">
              <QuestionTypeSelector question={question} onChange={onChange} />
              <DifficultieList question={question} onChange={onChange} />
            </div>
            <QuestionText question={question} onChange={onChange} />
            <DynamicAnswerEditor question={question} onChange={onChange} />
            <Tags
              question={question}
              tagInput={tagInput}
              setTagInput={setTagInput}
              onChange={onChange}
            />
          </>
        )}
      </div>
    </div>
  )
}

// Header with toggle
function Header({
  jsonMode,
  setJsonMode,
  onUpdate,
}: {
  jsonMode: boolean
  setJsonMode: React.Dispatch<React.SetStateAction<boolean>>
  onUpdate: () => void
}) {
  const { t } = useLocalization("question-editor", { en })

  return (
    <div className="flex justify-between items-center">
      <h3 className="text-sm font-medium">
        {t("questionManagement.editQuestion")}
      </h3>
      <div className="flex gap-2">
        <Button
          variant={jsonMode ? "default" : "outline"}
          size="sm"
          onClick={() => setJsonMode(!jsonMode)}
        >
          {jsonMode
            ? t("questionManagement.visualEditor")
            : t("questionManagement.switchToJSON")}
        </Button>
        <UpdateButton onUpdate={onUpdate} />
      </div>
    </div>
  )
}

function UpdateButton({ onUpdate }: { onUpdate: () => void }) {
  const { t } = useLocalization("question-editor", { en })

  return (
    <Button
      variant="default"
      onClick={onUpdate}
      data-testid="save-question-button"
    >
      {t("questionManagement.updateQuestion")}
    </Button>
  )
}

const QUESTION_TYPES = [
  { value: "multipleChoice", labelKey: "questionTypes.multipleChoice" },
  { value: "singleChoice", labelKey: "questionTypes.singleChoice" },
  { value: "textInput", labelKey: "questionTypes.textInput" },
  { value: "codeInput", labelKey: "questionTypes.codeInput" },
  { value: "imageBased", labelKey: "questionTypes.imageBased" },
  { value: "audioBased", labelKey: "questionTypes.audioBased" },
  { value: "audioAnswer", labelKey: "questionTypes.audioAnswer" },
  { value: "voiceInput", labelKey: "questionTypes.voiceInput" },
  { value: "fileUpload", labelKey: "questionTypes.fileUpload" },
]

function QuestionTypeSelector({
  question,
  onChange,
}: {
  question: Question
  onChange: (updatedQuestion: Partial<any>) => void
}) {
  const { t } = useLocalization("question-editor", { en })

  return (
    <div className="w-full">
      <label htmlFor="question-type" className="text-sm font-medium mb-2 block">
        {t("questionManagement.questionType")}
      </label>
      <Select
        defaultValue={question.type || "multipleChoice"}
        onValueChange={(value) => onChange({ type: value as Question["type"] })}
      >
        <SelectTrigger
          id="question-type"
          className="w-full"
          data-testid="question-type-select"
        >
          <SelectValue placeholder={t("questionManagement.questionType")} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {QUESTION_TYPES.map(({ value, labelKey }) => (
              <SelectItem key={value} value={value}>
                {t(labelKey)}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  )
}

function QuestionText({
  question,
  onChange,
}: {
  question: Question
  onChange: (updatedQuestion: Partial<any>) => void
}) {
  const { t } = useLocalization("question-editor", { en })
  const textareaRef = useRef<HTMLTextAreaElement | null>(null)

  const resizeTextarea = () => {
    const el = textareaRef.current
    if (el) {
      el.style.height = "auto"
      el.style.height = `${el.scrollHeight}px`
    }
  }

  useEffect(() => {
    resizeTextarea()
  }, [question.question])

  return (
    <div className="w-full">
      <label htmlFor="question-text" className="text-sm font-medium mb-2 block">
        {t("questionManagement.questionText")}
      </label>
      <Textarea
        id="question-text"
        data-testid="question-text-input"
        ref={textareaRef}
        className="min-h-[80px] resize-none overflow-hidden"
        value={question.question || ""}
        onChange={(e) => {
          onChange({ question: e.target.value })
          resizeTextarea()
        }}
        placeholder={t("questionManagement.enterQuestion")}
      />
    </div>
  )
}

function Tags({
  question,
  tagInput,
  setTagInput,
  onChange,
}: {
  question: Question
  tagInput: string
  setTagInput: React.Dispatch<React.SetStateAction<string>>
  onChange: (updatedQuestion: Partial<any>) => void
}) {
  const { t } = useLocalization("question-editor", { en })

  return (
    <div>
      <label className="text-sm font-medium mb-2 block">
        {t("questionManagement.tags")}
      </label>
      <div className="flex flex-wrap gap-2 mb-3">
        {question.tags?.map((tag, index) => (
          <Badge
            key={index}
            variant="secondary"
            className="flex items-center gap-1"
          >
            {tag}
            <X
              className="h-3 w-3 cursor-pointer"
              onClick={() => {
                const updatedTags = [...(question.tags || [])].filter(
                  (_, i) => i !== index,
                )
                onChange({ tags: updatedTags })
              }}
            />
          </Badge>
        ))}
      </div>
      <Input
        data-testid="tags-input"
        placeholder={t("questionManagement.addTag")}
        value={tagInput}
        onChange={(e) => setTagInput(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter" && tagInput.trim()) {
            const currentTags = question.tags || []
            onChange({ tags: [...currentTags, tagInput.trim()] })
            setTagInput("")
          }
        }}
      />
    </div>
  )
}

function DifficultieList({
  question,
  onChange,
}: {
  question: Question
  onChange: (updatedQuestion: Partial<any>) => void
}) {
  const { t } = useLocalization("question-editor", { en })

  return (
    <div className="w-full">
      <label className="text-sm font-medium mb-2 block">
        {t("questionManagement.difficulty")}
      </label>
      <Select
        defaultValue={question.difficulty || "MEDIUM"}
        onValueChange={(value) =>
          onChange({ difficulty: value as Question["difficulty"] })
        }
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t("questionManagement.selectDifficulty")} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="EASY">{t("questionManagement.easy")}</SelectItem>
            <SelectItem value="MEDIUM">
              {t("questionManagement.medium")}
            </SelectItem>
            <SelectItem value="HARD">{t("questionManagement.hard")}</SelectItem>
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  )
}
