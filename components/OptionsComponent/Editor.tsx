"use client"

import React, { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Trash2, Plus, ArrowU<PERSON>, ArrowDown } from "lucide-react"
import { locales } from "./locales"
import { useLocalization } from "@/src/localization/functions/client"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

export type EditableOption = {
  id: string
  text?: string
  audio?: string
  image?: string
  comment?: string
  correct?: boolean
}

export type OptionsEditorProps = {
  options: EditableOption[]
  onChange: (options: EditableOption[]) => void
  multipleCorrect?: boolean
}

const generateId = () => Math.random().toString(36).substring(2, 9)

type OptionType = "text" | "audio" | "image"

export const OptionsEditor: React.FC<OptionsEditorProps> = ({
  options,
  onChange,
  multipleCorrect = false,
}) => {
  const { t } = useLocalization("options-editor", locales)
  const [optionType, setOptionType] = useState<OptionType>("text")
  const [showComment, setShowComment] = useState(true)
  const [shuffleAnswers, setShuffleAnswers] = useState(false)

  const textAreaRefs = useRef<Record<string, HTMLTextAreaElement | null>>({})
  const optionRefs = useRef<Record<string, HTMLDivElement | null>>({})

  const autoResizeTextarea = (el: HTMLTextAreaElement | null) => {
    if (!el) return
    el.style.height = "auto"
    el.style.height = el.scrollHeight + "px"
  }

  useEffect(() => {
    const resetOptions = options.map((opt) => {
      const correct = opt.correct ?? false
      switch (optionType) {
        case "text":
          return {
            id: opt.id,
            text: opt.text || "",
            comment: opt.comment,
            correct,
          }
        case "audio":
          return {
            id: opt.id,
            audio: opt.audio || "",
            comment: opt.comment,
            correct,
          }
        case "image":
          return {
            id: opt.id,
            image: opt.image || "",
            comment: opt.comment,
            correct,
          }
        default:
          return opt
      }
    })
    onChange(resetOptions)
  }, [optionType])

  const capturePositions = () => {
    const newPositions: Record<string, DOMRect> = {}
    options.forEach((opt) => {
      const el = optionRefs.current[opt.id]
      if (el) newPositions[opt.id] = el.getBoundingClientRect()
    })
    return newPositions
  }

  const animateReorder = (
    oldPositions: Record<string, DOMRect>,
    newPositions: Record<string, DOMRect>,
  ) => {
    options.forEach((opt) => {
      const el = optionRefs.current[opt.id]
      if (!el) return

      const oldPos = oldPositions[opt.id]
      const newPos = newPositions[opt.id]
      if (!oldPos || !newPos) return

      const deltaY = oldPos.top - newPos.top
      if (deltaY) {
        el.style.transition = "none"
        el.style.transform = `translateY(${deltaY}px)`
        el.getBoundingClientRect() // Force reflow
        el.style.transition = "transform 300ms ease"
        el.style.transform = ""

        el.addEventListener("transitionend", () => (el.style.transition = ""), {
          once: true,
        })
      }
    })
  }

  const moveOption = (index: number, direction: "up" | "down") => {
    const newIndex = direction === "up" ? index - 1 : index + 1
    if (newIndex < 0 || newIndex >= options.length) return

    const oldPositions = capturePositions()
    const newOptions = [...options]
    const temp = newOptions[index]
    newOptions[index] = newOptions[newIndex]
    newOptions[newIndex] = temp
    onChange(newOptions)

    requestAnimationFrame(() => {
      const newPositions = capturePositions()
      animateReorder(oldPositions, newPositions)
    })
  }

  const handleAddOption = () => {
    const newOption: EditableOption =
      optionType === "text"
        ? { id: generateId(), text: "", correct: false }
        : optionType === "audio"
          ? { id: generateId(), audio: "", correct: false }
          : { id: generateId(), image: "", correct: false }

    onChange([...options, newOption])
  }

  const handleRemoveOption = (id: string) => {
    onChange(options.filter((opt) => opt.id !== id))
  }

  const handleOptionChange = (
    id: string,
    field: keyof EditableOption,
    value: string,
  ) => {
    onChange(
      options.map((opt) => (opt.id === id ? { ...opt, [field]: value } : opt)),
    )
  }

  const handleSelectCorrect = (id: string) => {
    if (multipleCorrect) {
      onChange(
        options.map((opt) =>
          opt.id === id ? { ...opt, correct: !opt.correct } : opt,
        ),
      )
    } else {
      onChange(
        options.map((opt) => ({
          ...opt,
          correct: opt.id === id,
        })),
      )
    }
  }

  return (
    <div className="space-y-6">
      {/* Option Type Selector */}
      <div className="grid gap-2">
        <Label>{t("optionTypeLabel")}</Label>
        <Select
          value={optionType}
          onValueChange={(v) => setOptionType(v as OptionType)}
        >
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder={t("optionTypeLabel")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="text">{t("optionTypeText")}</SelectItem>
            <SelectItem value="audio">{t("optionTypeAudio")}</SelectItem>
            <SelectItem value="image">{t("optionTypeImage")}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Toggles */}
      <div className="flex items-center gap-4">
        <div className="flex items-center space-x-2">
          <Switch
            id="show-comment"
            checked={showComment}
            onCheckedChange={setShowComment}
          />
          <Label htmlFor="show-comment">{t("showCommentField")}</Label>
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            id="shuffle-answers"
            checked={shuffleAnswers}
            onCheckedChange={setShuffleAnswers}
          />
          <Label htmlFor="shuffle-answers">{t("shuffleAnswers")}</Label>
        </div>
      </div>

      {/* Options */}
      {options.map((option, index) => (
        <div
          key={option.id}
          ref={(el) => {
            optionRefs.current[option.id] = el
          }}
          className="relative p-4 border rounded-lg bg-muted/50 space-y-3 flex items-start gap-4"
          style={{ willChange: "transform" }}
        >
          {/* Correct selection input */}
          <input
            type={multipleCorrect ? "checkbox" : "radio"}
            name={multipleCorrect ? undefined : "correctOption"}
            checked={!!option.correct}
            onChange={() => handleSelectCorrect(option.id)}
            className="mt-1"
            aria-label={t("selectCorrectOption", { index: index + 1 })}
          />

          <div className="flex-1 space-y-2">
            <div className="absolute right-2 top-2 flex gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => moveOption(index, "up")}
                disabled={index === 0}
              >
                <ArrowUp className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => moveOption(index, "down")}
                disabled={index === options.length - 1}
              >
                <ArrowDown className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveOption(option.id)}
              >
                <Trash2 className="w-4 h-4 text-red-500" />
              </Button>
            </div>

            <Label className="block font-medium">
              {t("optionLabel")} {String.fromCharCode(65 + index)}
            </Label>

            {optionType === "text" && (
              <Textarea
                value={option.text || ""}
                placeholder={t("textPlaceholder")}
                onChange={(e) => {
                  handleOptionChange(option.id, "text", e.target.value)
                  autoResizeTextarea(e.target)
                }}
                ref={(el) => {
                  textAreaRefs.current[option.id] = el
                  autoResizeTextarea(el)
                }}
                style={{ resize: "none", overflow: "hidden" }}
              />
            )}

            {optionType === "audio" && (
              <Input
                placeholder={t("audioPlaceholder")}
                value={option.audio || ""}
                onChange={(e) =>
                  handleOptionChange(option.id, "audio", e.target.value)
                }
              />
            )}

            {optionType === "image" && (
              <Input
                placeholder={t("imagePlaceholder")}
                value={option.image || ""}
                onChange={(e) =>
                  handleOptionChange(option.id, "image", e.target.value)
                }
              />
            )}

            {showComment && (
              <Textarea
                placeholder={t("commentPlaceholder")}
                value={option.comment || ""}
                onChange={(e) =>
                  handleOptionChange(option.id, "comment", e.target.value)
                }
              />
            )}
          </div>
        </div>
      ))}

      <div>
        <Button variant="outline" onClick={handleAddOption}>
          <Plus className="w-4 h-4 mr-2" />
          {t("addOption")}
        </Button>
      </div>
    </div>
  )
}
