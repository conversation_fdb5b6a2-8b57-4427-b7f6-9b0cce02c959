"use client"

import { useState } from "react"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import OverviewTab from "./tabs/overview-tab"
import QuestionsTab from "./tabs/questions-tab"
import CertificateTab from "./tabs/certificate-tab"
// import { testResultData } from "./test-result-data";
import { TestResultProvider } from "./testResultProvider"
import {
  RecommendationStep,
  TestQuestionInsight,
  TestResult,
} from "@/src/lib/repositories/test-result/types"
import { FinishBanner } from "./finish-banner"
import { PartialBanner } from "./partial-banner"
import Header from "@/src/app/LandingHeader"
import Footer from "@/src/app/LandingFooter"

export default function SkillTestResult({
  result,
  recommendations,
  questionAnalysis,
}: {
  result: TestResult
  recommendations: RecommendationStep[]
  questionAnalysis: TestQuestionInsight[]
}) {
  const [activeTab, setActiveTab] = useState("overview")
  const [isResultComplete, setIsResultComplete] = useState(true) // Set to false for partial results

  return (
    <TestResultProvider value={{ result, questionAnalysis }}>
      <div className="min-h-screen bg-gray-50" data-testid="test-results">
        {/* Header */}
        <Header />

        <div className="container mx-auto px-4 py-8">
          {/* Test Result Header */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">
                  {result.testTitle}
                </h1>
                <p className="text-gray-500">
                  Completed on {result.completedOn.toLocaleString()}
                </p>
              </div>
              <div className="flex gap-2">
                <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <circle cx="18" cy="5" r="3" />
                    <circle cx="6" cy="12" r="3" />
                    <circle cx="18" cy="19" r="3" />
                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49" />
                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49" />
                  </svg>
                  <span>Share</span>
                </button>
                <button className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                  <span>Download</span>
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="overview" className="mb-8">
            <TabsList className="mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="questions">Questions</TabsTrigger>
              <TabsTrigger value="certificate">Certificate</TabsTrigger>
            </TabsList>

            {/* Complete Result Status Banner */}
            {result.status === "complete" && (
              <FinishBanner title={result.testTitle} />
            )}

            {/* Partial Result Status Banner */}
            {result.status === "partial" && <PartialBanner />}

            <TabsContent value="overview">
              <OverviewTab
                testResult={result}
                recommendations={recommendations}
              />
            </TabsContent>

            <TabsContent value="questions">
              <QuestionsTab questionAnalysis={questionAnalysis} />
            </TabsContent>

            <TabsContent value="certificate">
              <CertificateTab testResult={result} />
            </TabsContent>
          </Tabs>
        </div>

        <Footer />
      </div>
    </TestResultProvider>
  )
}
