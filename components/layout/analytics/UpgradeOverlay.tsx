import React, { useEffect, useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Star, BadgeCheck } from "lucide-react"
import { Button } from "@/components/ui/button"
import { UpgradeOverlayType } from "@/src/lib/repositories/analytics/UpgradeOverlay/UpgradeOverlayRepository"
import { AnalyticsAPI } from "@/src/services/analyticsApi"

interface UpgradeOverlayProps {
  show: boolean
  title?: string
  description?: string
  badgeText?: string
  buttonText?: string
  onUpgrade?: () => void
}

export default function UpgradeOverlay({
  show,
  title = "Analytics Dashboard",
  description = "Unlock powerful analytics to track your performance and growth",
  badgeText = "Basic Feature",
  buttonText = "Upgrade to Basic",
  onUpgrade,
}: UpgradeOverlayProps) {
  const [upgradeOverlay, setUpgradeOverlay] = useState<UpgradeOverlayType>({
    features: [],
    plan: {
      name: "",
      description: "",
      price: "",
    },
  })
  const [loading, setLoading] = useState(true)

  const fetchTests = async () => {
    try {
      const data = await AnalyticsAPI.AnalyticsUpgradeOverlay({
        page: 1,
      }).request()
      setUpgradeOverlay(data)
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTests()
  }, [])

  if (!show) return null
  return (
    <div
      className="absolute inset-0 flex items-center justify-center"
      data-testid="upgrade-prompt"
    >
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">{title}</CardTitle>
            <div className="px-2 py-1 bg-amber-100 text-amber-800 rounded-full text-xs font-medium">
              {badgeText}
            </div>
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-3">
            {upgradeOverlay?.features.map((feature) => (
              <div key={feature.id} className="flex items-center gap-2">
                <BadgeCheck className="h-5 w-5 text-green-600" />
                <span>{feature.text}</span>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between bg-muted p-4 rounded-lg">
            <div>
              <p className="font-medium">{upgradeOverlay?.plan?.name}</p>
              <p className="text-sm text-muted-foreground">
                {upgradeOverlay?.plan?.description}
              </p>
            </div>
            <div className="text-right">
              <p className="font-bold text-lg">{upgradeOverlay?.plan?.price}</p>
              {upgradeOverlay?.plan?.priceUnit && (
                <p className="text-xs text-muted-foreground">
                  {upgradeOverlay?.plan?.priceUnit}
                </p>
              )}
            </div>
          </div>

          <Button
            className="w-full"
            onClick={onUpgrade}
            data-testid="upgrade-to-pro-button"
          >
            <Star className="mr-2 h-4 w-4" />
            {buttonText}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
