"use client"

import React, { useState } from "react"
import {
  MoreHorizontal,
  Tag,
  Clock,
  FileText,
  HelpCircle,
  Code,
  CheckSquare,
  MessageSquare,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useLocalization } from "@/src/localization/functions/client"
import Link from "next/link"
import { locales } from "../locales"
import { Question } from "@/src/lib/repositories/questions/interface"

interface QuestionCardProps {
  item: Question
  handleDelete: (item: Question) => void
  handleClone: (item: Question) => void
}

function getQuestionTypeIcon(type: Question["type"]) {
  switch (type) {
    case "multipleChoice":
      return <CheckSquare className="h-4 w-4" />
    case "singleChoice":
      return <MessageSquare className="h-4 w-4" />
    case "codeInput":
      return <Code className="h-4 w-4" />
    case "textInput":
      return <FileText className="h-4 w-4" />
    default:
      return <HelpCircle className="h-4 w-4" />
  }
}

function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

export default function QuestionCard({
  item,
  handleDelete,
  handleClone,
}: QuestionCardProps) {
  const { t } = useLocalization("public-test", locales)
  const [error, setError] = useState<string | null>(null)

  function onDelete() {
    if (confirm(t("confirmDelete") || "Are you sure you want to delete?")) {
      try {
        handleDelete(item)
      } catch (err: any) {
        setError(err.message || "Unknown error")
      }
    }
  }

  function onClone() {
    try {
      handleClone(item)
    } catch (err: any) {
      setError(err.message || "Unknown error")
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            {getQuestionTypeIcon(item.type)}
            <CardTitle className="text-base font-medium line-clamp-1">
              {item.question}
            </CardTitle>
            {item.difficulty && (
              <Badge
                variant="outline"
                className={`
                  ${
                    item.difficulty === "EASY"
                      ? "bg-green-50 text-green-700 border-green-200"
                      : item.difficulty === "MEDIUM"
                        ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                        : item.difficulty === "HARD"
                          ? "bg-red-50 text-red-700 border-red-200"
                          : "bg-gray-50 text-gray-700 border-gray-200"
                  }
                `}
              >
                {item.difficulty.toUpperCase()}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="text-sm text-muted-foreground flex gap-2 mb-3">
          <span className="capitalize">
            {item.type} {t("card.question")}
          </span>
          <span>•</span>
          <span className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {formatDate(item.updatedAt)}
          </span>
        </div>

        <div className="flex flex-wrap gap-2">
          {item.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>

        {error && <p className="text-red-600 text-sm mt-2">{error}</p>}
      </CardContent>

      <CardFooter className="pt-0 flex justify-between">
        <Link href={`/dashboard/question-editor/q/${item.id}`}>
          <Button variant="outline" size="sm">
            {t("card.editQuestion")}
          </Button>
        </Link>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {/* <DropdownMenuItem>{t("card.addToBank")}</DropdownMenuItem>
            <DropdownMenuItem>{t("card.addToTest")}</DropdownMenuItem> */}
            <DropdownMenuItem onClick={onClone}>
              {t("card.duplicate")}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onDelete} className="text-red-600">
              {t("card.delete")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  )
}
