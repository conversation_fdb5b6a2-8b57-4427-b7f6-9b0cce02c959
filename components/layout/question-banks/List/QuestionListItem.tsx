"use client"

import React, { useState } from "react"
import {
  MoreHorizontal,
  Tag,
  Clock,
  FileText,
  HelpCircle,
  Code,
  CheckSquare,
  MessageSquare,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { useLocalization } from "@/src/localization/functions/client"
import Link from "next/link"
import { Question } from "@/src/lib/repositories/questions/interface"
import { locales } from "../locales"

interface QuestionListItemProps {
  item: Question
  handleDelete: (item: Question) => void
  handleClone: (item: Question) => void
}

function getQuestionTypeIcon(type: Question["type"]) {
  switch (type) {
    case "multipleChoice":
      return <CheckSquare className="h-4 w-4" />
    case "singleChoice":
      return <MessageSquare className="h-4 w-4" />
    case "codeInput":
      return <Code className="h-4 w-4" />
    case "textInput":
      return <FileText className="h-4 w-4" />
    default:
      return <HelpCircle className="h-4 w-4" />
  }
}

function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

export default function QuestionListItem({
  item,
  handleDelete,
  handleClone,
}: QuestionListItemProps) {
  const { t } = useLocalization("public-test", locales)
  const [error, setError] = useState<string | null>(null)

  function onDelete() {
    if (!confirm(t("confirmDelete") || "Are you sure you want to delete?"))
      return

    try {
      handleDelete(item)
    } catch (err: any) {
      setError(err.message || "Unknown error")
    }
  }

  function onClone() {
    try {
      handleClone(item)
    } catch (err: any) {
      setError(err.message || "Unknown error")
    }
  }

  return (
    <div className="flex items-start justify-between border rounded-lg p-4 hover:bg-muted/50 bg-white shadow-sm">
      <div className="flex-1 pr-4">
        <div className="flex items-center gap-2 mb-1">
          {getQuestionTypeIcon(item.type)}
          <h3 className="font-medium text-base line-clamp-1">
            {item.question}
          </h3>
          {item.difficulty && (
            <Badge
              variant="outline"
              className={`
                ${
                  item.difficulty === "EASY"
                    ? "bg-green-50 text-green-700 border-green-200"
                    : item.difficulty === "MEDIUM"
                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                      : item.difficulty === "HARD"
                        ? "bg-red-50 text-red-700 border-red-200"
                        : "bg-gray-50 text-gray-700 border-gray-200"
                }`}
            >
              {item.difficulty.toUpperCase()}
            </Badge>
          )}
        </div>

        <div className="flex items-center text-sm text-gray-500 gap-2 mb-2">
          <span className="capitalize">
            {item.type} {t("card.question")}
          </span>
          <span>•</span>
          <span className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {formatDate(item.updatedAt)}
          </span>
        </div>

        <div className="flex flex-wrap gap-2">
          {item.tags.map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </div>

        {error && <p className="text-red-600 text-sm mt-2">{error}</p>}
      </div>

      <div className="flex flex-col items-end justify-between h-full gap-2">
        <Link href={`/dashboard/question-editor/q/${item.id}`}>
          <Button variant="outline" size="sm">
            {t("card.editQuestion")}
          </Button>
        </Link>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="end">
            <DropdownMenuItem>{t("card.addToBank")}</DropdownMenuItem>
            <DropdownMenuItem>{t("card.addToTest")}</DropdownMenuItem>
            <DropdownMenuItem onClick={onClone}>
              {t("card.duplicate")}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onDelete} className="text-red-600">
              {t("card.delete")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}
