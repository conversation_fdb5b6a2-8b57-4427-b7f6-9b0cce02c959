"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useTestData } from "./testDataProvider"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"
import { TestSessionsAPI } from "@/src/services/testSessionApi"

export function TestLandingCta() {
  const testData = useTestData()
  const [loading, setLoading] = useState(false)
  const [userId, setUserId] = useState<string | null>(null)
  const [userName, setUserName] = useState<string | null>(null)
  const [userEmail, setUserEmail] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    const storedUserId = localStorage.getItem("user_id")
    const storedName = localStorage.getItem("fullName")
    const storedEmail = localStorage.getItem("email")

    setUserId(storedUserId)
    setUserName(storedName)
    setUserEmail(storedEmail)
  }, [])

  const handleStartTest = async () => {
    if (!userId || !userName || !userEmail) {
      alert("User not found. Please join the test first.")
      router.push("/join-test?testId=" + testData.id)
      return
    }

    setLoading(true)
    try {
      const response = await TestSessionsAPI.CreateTestSession({
        testId: testData.id,
        user: {
          id: userId,
          name: userName,
          email: userEmail,
        },
        createdBy: userId, // Required field for the new API
      }).request()

      if (response?.status === "success" && response?.data) {
        router.push(`/test-session/${response.data.id}`)
      } else {
        console.log("FAILED start session", response)
        alert("Failed to start test session")
      }
    } catch (err) {
      console.error("Error starting test session:", err)
      alert("Something went wrong while starting the test")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex flex-col sm:flex-row justify-center gap-4 mb-10">
      <Link href="/tests">
        <Button variant="outline" className="rounded-full px-6 py-2 h-12">
          Maybe later
        </Button>
      </Link>

      <Button
        onClick={handleStartTest}
        disabled={loading}
        className="bg-green-600 hover:bg-green-700 rounded-full px-6 py-2 h-12 text-white flex items-center gap-2"
        data-testid="begin-test-button"
      >
        {loading && <Loader2 className="animate-spin h-5 w-5" />}
        Start Your Test
      </Button>
    </div>
  )
}
