"use client"

import { useState } from "react"
import { <PERSON>R<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface Props {
  fullName: string
  email: string
  invitationCode?: string
  testTitle?: string
  hideInvitationCode?: boolean
  isSubmitting?: boolean
  error?: string | null
  onChangeName: (val: string) => void
  onChangeEmail: (val: string) => void
  onJoin: () => void
  onBack: () => void
}

export default function JoinForm({
  fullName,
  email,
  testTitle,
  invitationCode = "",
  hideInvitationCode = false,
  isSubmitting = false,
  error: externalError = null,
  onChangeName,
  onChangeEmail,
  onJoin,
  onBack,
}: Props) {
  const [localError, setLocalError] = useState("")

  const handleSubmit = () => {
    if (!fullName.trim() || !email.trim()) {
      setLocalError("Please enter your full name and email.")
      return
    }
    setLocalError("")
    onJoin()
  }

  const displayError = externalError || localError

  return (
    <div className="p-8 md:p-12">
      <div className="w-full p-0 md:px-20">
        <h1 className="text-4xl font-bold mb-3">Join Test</h1>
        {testTitle && (
          <p className="text-gray-500 mb-2">You're joining: {testTitle}</p>
        )}
        <p className="text-gray-500 mb-8">
          Unlock your potential with every question.
        </p>

        <div className="flex flex-col space-y-6">
          <div className="flex flex-col space-y-4">
            <div>
              <label
                htmlFor="fullName"
                className="text-sm font-medium mb-1 block"
              >
                Full Name
              </label>
              <Input
                id="fullName"
                data-testid="guest-name-input"
                placeholder="Your name"
                className="h-12 rounded-md"
                value={fullName}
                onChange={(e) => onChangeName(e.target.value)}
              />
            </div>

            <div>
              <label htmlFor="email" className="text-sm font-medium mb-1 block">
                Email
              </label>
              <Input
                id="email"
                data-testid="guest-email-input"
                placeholder="Your email"
                className="h-12 rounded-md"
                value={email}
                onChange={(e) => onChangeEmail(e.target.value)}
              />
            </div>

            {!hideInvitationCode && (
              <div>
                <label
                  htmlFor="invitationCode"
                  className="text-sm font-medium mb-1 block"
                >
                  Invitation Code
                </label>
                <Input
                  id="invitationCode"
                  placeholder="Invitation Code"
                  className="h-12 rounded-md"
                  value={invitationCode}
                  disabled
                  readOnly
                />
              </div>
            )}
          </div>

          {displayError && (
            <p className="text-red-600 text-sm mt-1">{displayError}</p>
          )}

          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-full h-12 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            data-testid="start-test-button"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Joining...
              </>
            ) : (
              <>
                Join Test <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
