"use client"

import { useEffect, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/src/hooks/useAuth"
import JoinForm from "./JoinForm"
import JoinIllustration from "./JoinIllustration"
import { PublicTestsAPI } from "@/src/services/publicTestApi"

// Fetch test information from API
async function getTestByIdentifier(identifier: string): Promise<{
  testId: string
  title: string
  description?: string
  error?: string
}> {
  try {
    const result = await PublicTestsAPI.TestDetail(identifier).request()

    return {
      testId: result.id,
      title: result.title,
      description: result.description,
    }
  } catch (error) {
    console.error("Failed to fetch test:", error)
    return { testId: "", title: "", error: "Network error occurred" }
  }
}

export default function JoinExam() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { isAuthenticated, isLoading, createTemporaryAuth, checkAuthState } =
    useAuth()
  const code = searchParams.get("code")
  const directTestId = searchParams.get("testId")

  const [fullName, setFullName] = useState("")
  const [email, setEmail] = useState("")
  const [testId, setTestId] = useState<string | null>(null)
  const [testTitle, setTestTitle] = useState("")
  const [invitationCode, setInvitationCode] = useState<string | null>(null)
  const [hideInvitationCode, setHideInvitationCode] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoadingTest, setIsLoadingTest] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Check if user is already authenticated
  useEffect(() => {
    if (isAuthenticated && testId) {
      // User is already authenticated, redirect to test
      router.push(`/test-landing/${testId}`)
      return
    }

    // Load saved form data
    const savedName = localStorage.getItem("fullName")
    const savedEmail = localStorage.getItem("email")
    if (savedName) setFullName(savedName)
    if (savedEmail) setEmail(savedEmail)
  }, [isAuthenticated, testId, router])

  useEffect(() => {
    const fetchTestInfo = async () => {
      if (directTestId || code) {
        setIsLoadingTest(true)
        setError(null)

        const identifier = directTestId || code
        const result = await getTestByIdentifier(identifier!)

        if (result.error) {
          setError(result.error)
          setTestId("")
          setTestTitle("")
        } else {
          setTestId(result.testId)
          setTestTitle(result.title)
          if (directTestId) {
            setHideInvitationCode(true)
          } else {
            setInvitationCode(code)
            setHideInvitationCode(false)
          }
        }

        setIsLoadingTest(false)
      }
    }

    fetchTestInfo()
  }, [code, directTestId])

  const handleJoin = async () => {
    if (!fullName.trim() || !email.trim()) {
      setError("Please enter your full name and email")
      return
    }

    if (!testId) {
      setError("Invalid test information. Please check your invitation link.")
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Create temporary authentication
      await createTemporaryAuth({
        email: email.trim(),
        name: fullName.trim(),
        testId,
      })

      // Save form data to localStorage for future use
      localStorage.setItem("fullName", fullName)
      localStorage.setItem("email", email)

      // Redirect to the test landing page
      router.push(`/test-landing/${testId}`)
    } catch (error: any) {
      console.error("Join test failed:", error)
      setError(error.message || "Failed to join test. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Show loading state while checking authentication or loading test
  if (isLoading || isLoadingTest) {
    return (
      <div className="bg-gray-100 flex flex-col items-center justify-center min-h-screen">
        <div className="bg-white rounded-3xl shadow-sm p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mr-3"></div>
            <span className="text-lg">
              {isLoading
                ? "Checking authentication..."
                : "Loading test information..."}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // Show error state if test not found or other errors
  if (error && !testId) {
    return (
      <div className="bg-gray-100 flex flex-col items-center justify-center min-h-screen">
        <div className="bg-white rounded-3xl shadow-sm p-8 max-w-md mx-auto text-center">
          <div className="text-red-600 mb-4">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold mb-2 text-gray-900">
            Test Not Found
          </h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.history.back()}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-100 flex flex-col">
      <div className="p-4 md:p-8 h-full w-full">
        <div className="bg-white rounded-3xl shadow-sm h-full w-full overflow-clip">
          <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
            <JoinForm
              fullName={fullName}
              email={email}
              invitationCode={invitationCode ?? ""}
              testTitle={testTitle}
              hideInvitationCode={hideInvitationCode}
              isSubmitting={isSubmitting}
              error={error}
              onChangeName={setFullName}
              onChangeEmail={setEmail}
              onJoin={handleJoin}
              onBack={() => router.back()}
            />
            <JoinIllustration />
          </div>
        </div>
      </div>
    </div>
  )
}
