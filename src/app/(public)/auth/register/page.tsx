"use client"

import { useLocalization } from "@/src/localization/functions/client"
import { AuthAPI } from "@/src/services/authApi"
import { useState } from "react"
import { authLocales } from "../locales"
import Link from "next/link"

export default function RegisterPage() {
  const { t } = useLocalization("auth", authLocales)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [name, setName] = useState("")
  const [message, setMessage] = useState<string | null>(null)
  const [isError, setIsError] = useState(false)

  const validateEmail = (email: string) => {
    // Simple email regex check
    return /^\S+@\S+\.\S+$/.test(email)
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)
    setIsError(false)

    if (!name.trim()) {
      setMessage(t("auth.name_required") || "Name is required")
      setIsError(true)
      return
    }
    if (!email.trim()) {
      setMessage(t("auth.email_required") || "Email is required")
      setIsError(true)
      return
    }
    if (!validateEmail(email)) {
      setMessage(t("auth.invalid_email_format") || "Invalid email format")
      setIsError(true)
      return
    }
    if (!password) {
      setMessage(t("auth.password_required") || "Password is required")
      setIsError(true)
      return
    }

    try {
      await AuthAPI.Register({ email, password, name }).request()
      setMessage(t("auth.register_success") || "Registration successful")
      setIsError(false)
      // Optionally redirect after a short delay
      // setTimeout(() => window.location.href = "/auth/login", 2000)
    } catch (err: any) {
      setMessage(
        err?.message || t("auth.register_error") || "Registration failed",
      )
      setIsError(true)
    }
  }

  return (
    <div
      className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md"
      data-testid="registration-page"
    >
      <h1 className="text-2xl font-semibold mb-4">{t("auth.register")}</h1>
      <form
        onSubmit={handleRegister}
        className="space-y-4"
        data-testid="register-form"
      >
        <input
          name="name"
          data-testid="name-input"
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.name")}
          value={name}
          onChange={(e) => setName(e.target.value)}
          autoComplete="name"
        />
        <input
          name="email"
          data-testid="email-input"
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.email")}
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          autoComplete="email"
        />
        <input
          name="password"
          data-testid="password-input"
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.password")}
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          autoComplete="new-password"
        />
        <button
          type="submit"
          data-testid="register-button"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.register")}
        </button>
        {message && (
          <p
            data-testid={isError ? "error-message" : "success-message"}
            className={`text-sm mt-2 ${isError ? "text-red-600" : "text-green-600"}`}
          >
            {message}
          </p>
        )}
      </form>

      <div className="mt-4 text-center">
        <p className="text-sm">
          {t("auth.have_account")}{" "}
          <Link
            href="/auth/login"
            className="text-blue-600 hover:underline"
            data-testid="login-link"
          >
            {t("auth.login")}
          </Link>
        </p>
      </div>
    </div>
  )
}
