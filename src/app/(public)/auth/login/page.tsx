"use client"

import { useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"

import { useLocalization } from "@/src/localization/functions/client"
import { authLocales } from "../locales"
import { AuthAPI } from "@/src/services/authApi"
import { secureStorage, StorageKeys } from "@/src/utils/SecureStorage"

export default function LoginPage() {
  const { t } = useLocalization("auth", authLocales)
  const searchParams = useSearchParams()

  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { token, refresh_token } = await AuthAPI.Login({
        email,
        password,
      }).request()

      secureStorage.setItem(StorageKeys.UserToken, token)
      secureStorage.setItem(StorageKeys.CookieToken, token)
      secureStorage.setItem(StorageKeys.RefreshToken, refresh_token)

      const redirectTo = searchParams.get("redirectTo")
      window.location.href = redirectTo || "/dashboard"
    } catch (err: any) {
      setError(err?.message || t("auth.login_error"))
    }
  }

  return (
    <div
      className="max-w-md mx-auto mt-20 p-6 bg-white shadow rounded-md"
      data-testid="login-page"
    >
      <h1 className="text-2xl font-semibold mb-4">{t("auth.login")}</h1>
      <form
        onSubmit={handleLogin}
        className="space-y-4"
        data-testid="login-form"
      >
        <input
          name="email"
          data-testid="email-input"
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.email")}
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          name="password"
          data-testid="password-input"
          className="w-full border border-gray-300 px-4 py-2 rounded"
          placeholder={t("auth.password")}
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <button
          type="submit"
          data-testid="login-button"
          className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
        >
          {t("auth.login")}
        </button>
        {error && (
          <p className="text-sm text-red-600" data-testid="error-message">
            {error}
          </p>
        )}
      </form>

      <div className="mt-6 flex justify-between items-center text-sm text-blue-600">
        <Link
          href="/auth/register"
          className="hover:underline"
          data-testid="register-link"
        >
          {t("auth.register")}
        </Link>
        <Link
          href="/auth/forgot-password"
          className="hover:underline"
          data-testid="forgot-password-link"
        >
          {t("auth.forgot_password")}
        </Link>
      </div>
    </div>
  )
}
