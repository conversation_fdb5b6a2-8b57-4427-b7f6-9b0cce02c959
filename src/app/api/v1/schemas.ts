import { z } from "zod"

// Common schemas
export const paginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
})

// User/Participant schemas
export const participantSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional(),
})

export const creatorSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional(),
})

export const instructorSchema = z.object({
  name: z.string(),
  avatar: z.string(),
})

export const testUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
})

// Test schemas
export const testSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  description: z.string().optional(),
  status: z.enum([
    "ACTIVE",
    "INACTIVE",
    "ARCHIVED",
    "DRAFT",
    "INPROGRESS",
    "DONE",
    "CANCELLED",
  ]),
  categories: z.array(z.string()).optional(),
  difficulties: z.array(z.string()).optional(),
  benefits: z.array(z.string()),
  usersCount: z.number(),
  successRate: z.number().optional(),
  durationInSeconds: z.number().optional(),
  totalQuestions: z.number(),
  image: z.string().optional(),
  mediaUrl: z.string().optional(),
  reason: z.string().optional(),
  creator: creatorSchema,
  participants: z.array(participantSchema).optional(),
})

// Course schemas
export const sectionSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  duration: z.string().optional(),
  completed: z.boolean().optional(),
})

export const courseSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  summary: z.string(),
  students: z.number().min(0),
  completionRate: z.number().min(0).max(100),
  engagement: z.number().min(0).max(100),
  description: z.string(),
})

export const courseDetailSchema = courseSchema.extend({
  course_id: z.string(),
  sections: z.array(sectionSchema),
})

export const courseCommentSchema = z.object({
  id: z.string().optional(),
  courseId: z.string(),
  userId: z.string(),
  userName: z.string(),
  userAvatar: z.string().optional(),
  content: z.string().min(1),
  timestamp: z.date(),
  likes: z.number().min(0).default(0),
  dislikes: z.number().min(0).default(0),
  isLiked: z.boolean().default(false),
  isDisliked: z.boolean().default(false),
  isFlagged: z.boolean().default(false),
  isRead: z.boolean().default(false),
})

// Question schemas
export const optionTextSchema = z.object({
  id: z.string(),
  text: z.string(),
})

export const optionAudioSchema = z.object({
  id: z.string(),
  audio: z.string(),
})

export const baseQuestionSchema = z.object({
  id: z.string().optional(),
  type: z.enum([
    "multipleChoice",
    "singleChoice",
    "textInput",
    "codingChallenge",
    "voiceInput",
    "fileUpload",
  ]),
  difficulty: z.string(),
  question: z.string().min(1),
  internal: z.record(z.object({})),
  tags: z.array(z.string()),
})

export const multipleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("multipleChoice"),
  options: z.array(optionTextSchema),
  correctAnswers: z.array(z.string()),
})

export const singleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("singleChoice"),
  options: z.array(optionTextSchema),
  correctAnswer: z.string(),
})

export const textInputQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("textInput"),
  reviewGuide: z.string(),
  caseSensitive: z.boolean(),
})

export const questionSchema = z.discriminatedUnion("type", [
  multipleChoiceQuestionSchema,
  singleChoiceQuestionSchema,
  textInputQuestionSchema,
])

// Notification schemas
export const notificationSchema = z.object({
  id: z.string().optional(),
  type: z.enum([
    "test_completed",
    "badge_earned",
    "course_recommendation",
    "system",
    "achievement",
    "test_reminder",
    "course_update",
    "social",
  ]),
  title: z.string().min(1),
  description: z.string(),
  date: z.date(),
  read: z.boolean().default(false),
  icon: z.string(),
  iconBg: z.string(),
  link: z.string(),
})

// Collaborator schemas
export const roleSchema = z.enum(["admin", "team", "external"])
export const statusCollaboratorSchema = z.enum(["ACTIVE", "INACTIVE"])
export const statusInviteSchema = z.enum(["PENDING", "ACCEPTED", "DECLINED"])

export const invitationSchema = z.object({
  id: z.string().optional(),
  email: z.string().email(),
  role: roleSchema,
  status: statusInviteSchema,
  expiresAt: z.date(),
  sentAt: z.date(),
  code: z.string(),
  invitedBy: z.string().optional(),
})

export const collaboratorUserSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1),
  email: z.string().email(),
  avatar: z.string(),
  role: roleSchema,
  status: statusInviteSchema,
  lastActive: z.date(),
  courses: z.number().min(0),
})

// My Courses schemas
export const myCourseSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  instructor: z.string(),
  instructorAvatar: z.string(),
  completionPercentage: z.number().min(0).max(100),
  lastAccessed: z.date().optional(),
  summary: z.string(),
  coverImage: z.string(),
  totalLessons: z.number().min(0),
  completedLessons: z.number().min(0),
  estimatedHours: z.number().min(0),
  category: z.array(z.string()),
})

export const myCourseDetailSchema = myCourseSchema.extend({
  my_courses_id: z.string(),
  rating: z.number().min(0).max(5),
  totalReviews: z.number().min(0),
  sections: z.array(sectionSchema),
})

export const videoCommentSchema = z.object({
  id: z.string().optional(),
  videoId: z.string(),
  userId: z.string(),
  userName: z.string(),
  userAvatar: z.string().optional(),
  content: z.string().min(1),
  videoTimestamp: z.string(),
  likes: z.number().min(0).default(0),
  dislikes: z.number().min(0).default(0),
  isLiked: z.boolean().default(false),
  isDisliked: z.boolean().default(false),
})

export const myCourseNoteSchema = z.object({
  id: z.string().optional(),
  video_id: z.string(),
  section_id: z.string(),
  title: z.string().min(1),
  content: z.string().min(1),
  videoTimestamp: z.string(),
})

export const myCourseFeedbackSchema = z.object({
  courseId: z.string(),
  rating: z.number().min(1).max(5),
  feedback: z.string().min(1),
})

// My Tests schemas
export const myTestSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  category: z.string(),
  badges: z.array(z.string()),
  correctAnswers: z.number().min(0),
  score: z.number().min(0),
  instructor: instructorSchema,
  totalQuestions: z.number().min(0),
  date: z.date(),
  duration: z.string().optional(),
  image: z.string().optional(),
})

// Test Session schemas
export const testSessionSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1),
  status: z.enum(["CLOSED", "OPEN"]),
  description: z.string(),
  benefits: z.array(z.string()),
  duration: z.number().min(0),
  categories: z.array(z.string()),
  difficulty: z.string(),
  totalQuestions: z.number().min(0),
  user: testUserSchema,
})

export const submittedAnswerSchema = z.object({
  questionId: z.string(),
  answer: z.union([z.string(), z.array(z.string())]),
})

export const exitDataSchema = z.object({
  userId: z.string().optional(),
  timeSpent: z.number().min(0),
  exitReason: z.string().min(1),
  exitedAt: z.date(),
})

// Error codes
export const ERROR_CODES = {
  GET_ALL_FAILED: "ERROR_GET_ALL_FAILED",
  GET_BY_ID_FAILED: "ERROR_GET_BY_ID_FAILED",
  CREATE_FAILED: "ERROR_CREATE_FAILED",
  UPDATE_FAILED: "ERROR_UPDATE_FAILED",
  DELETE_FAILED: "ERROR_DELETE_FAILED",
  VALIDATION_FAILED: "ERROR_VALIDATION_FAILED",
  NOT_FOUND: "ERROR_NOT_FOUND",
  UNKNOWN_ERROR: "ERROR_UNKNOWN",
} as const

// Settings schemas
export const userSettingsSchema = z.object({
  id: z.string().optional(),
  userId: z.string(),

  // Notification Settings
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(true),
  courseUpdates: z.boolean().default(true),
  testReminders: z.boolean().default(true),
  collaboratorActivity: z.boolean().default(false),
  notificationFrequency: z
    .enum(["immediate", "daily", "weekly"])
    .default("immediate"),

  // Appearance Settings
  theme: z.enum(["light", "dark", "system"]).default("system"),
  colorScheme: z
    .enum(["blue", "green", "purple", "orange", "red"])
    .default("blue"),
  fontSize: z.enum(["small", "MEDIUM", "large"]).default("MEDIUM"),
  compactMode: z.boolean().default(false),
  animations: z.boolean().default(true),

  // Language Settings
  language: z.string().default("en"),
  region: z.string().default("US"),
  dateFormat: z.string().default("MM/DD/YYYY"),
  timeFormat: z.enum(["12", "24"]).default("12"),

  // Security Settings
  twoFactorEnabled: z.boolean().default(false),
  sessionTimeout: z.number().min(5).max(480).default(30), // 5 minutes to 8 hours

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
})

export const notificationSettingsSchema = z.object({
  emailNotifications: z.boolean().optional(),
  pushNotifications: z.boolean().optional(),
  courseUpdates: z.boolean().optional(),
  testReminders: z.boolean().optional(),
  collaboratorActivity: z.boolean().optional(),
  notificationFrequency: z.enum(["immediate", "daily", "weekly"]).optional(),
})

export const appearanceSettingsSchema = z.object({
  theme: z.enum(["light", "dark", "system"]).optional(),
  colorScheme: z.enum(["blue", "green", "purple", "orange", "red"]).optional(),
  fontSize: z.enum(["small", "MEDIUM", "large"]).optional(),
  compactMode: z.boolean().optional(),
  animations: z.boolean().optional(),
})

export const languageSettingsSchema = z.object({
  language: z.string().optional(),
  region: z.string().optional(),
  dateFormat: z.string().optional(),
  timeFormat: z.enum(["12", "24"]).optional(),
})

export const securitySettingsSchema = z.object({
  twoFactorEnabled: z.boolean().optional(),
  sessionTimeout: z.number().min(5).max(480).optional(),
})
