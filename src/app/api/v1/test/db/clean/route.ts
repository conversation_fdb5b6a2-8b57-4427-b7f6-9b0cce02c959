import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections"
import { driver } from "@/src/lib/repositories/LiveMongoDriver"
import { ResponseWrapper } from "@/src/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"

export async function POST(req: NextRequest) {
    try {
        const collections = Object.values(MONGO_COLLECTIONS)
        const cleanedCollections: string[] = []
        const deleted: any = {}

        for (const collectionName of collections) {
            const collection = await driver.getCollection(collectionName)
            const result = await collection.deleteMany({})
            deleted[collectionName] = result.deletedCount
            cleanedCollections.push(collectionName)
        }

        return NextResponse.json(new ResponseWrapper("success", {
            message: "Cleaned up all collections",
            deleted, cleanedCollections
        }))
    } catch (error) {
        console.error("Cleanup error:", error)
        return NextResponse.json(
            {
                status: "failed",
                error: "Cleanup failed",
            },
            { status: 500 },
        )
    }
}
