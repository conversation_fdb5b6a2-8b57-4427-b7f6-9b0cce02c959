import { MONGO_COLLECTIONS } from "@/src/lib/db/mongoCollections"
import { driver } from "@/src/lib/repositories/LiveMongoDriver"
import { ResponseWrapper } from "@/src/lib/types/responseWrapper"
import { NextRequest, NextResponse } from "next/server"
import z from "zod"
import { ERROR_CODES } from "../../../schemas"

const seedSchema = z.object({
  collection: z.string().min(1, "Collection name is required"),
  data: z.array(z.any()).min(1, "Data is required"),
})

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()

    // Validate request body
    const validation = seedSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        new ResponseWrapper(
          "failed",
          null,
          validation.error.errors.map(
            (err) => `${err.path.join(".")}: ${err.message}`,
          ),
          [ERROR_CODES.VALIDATION_FAILED],
        ),
        { status: 400 },
      )
    }

    const { collection, data } = validation.data

    const collectionObj = driver.getCollection(collection)
    const result = await collectionObj.insertMany(data)

    return NextResponse.json(
      new ResponseWrapper("success", {
        message: `Inserted ${result.insertedCount} documents`,
        ids: result.insertedIds,
      }),
    )
  } catch (error) {
    console.error("Seed error:", error)
    return NextResponse.json(
      {
        status: "failed",
        error: "Seed failed",
      },
      { status: 500 },
    )
  }
}
