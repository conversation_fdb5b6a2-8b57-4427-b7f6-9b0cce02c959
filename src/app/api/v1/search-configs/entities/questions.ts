import MESSAGE_KEYS from "@/src/app/api/message_keys"
import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { StringSort } from "../sorts/StringSort"
import { NumberSort } from "../sorts/NumberSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { ERROR_KEYS } from "./errors"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class QuestionsSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const filters = [
      new StringSearch("question", MESSAGE_KEYS.SEARCH_CONFIG.QUESTION_TEXT, {
        validations: [
          (value: string) => {
            if (value.length == 0)
              throw new Error(ERROR_KEYS.QUESTION_MUST_NOT_EMPTY)
          },
        ],
      }),

      new MultipleSelectSearch(
        "type",
        MESSAGE_KEYS.SEARCH_CONFIG.QUESTION_TYPE,
        [
          {
            value: "multiple_choice",
            label: MESSAGE_KEYS.SEARCH_CONFIG.TYPE_MULTIPLE_CHOICE,
          },
          {
            value: "true_false",
            label: MESSAGE_KEYS.SEARCH_CONFIG.TYPE_TRUE_FALSE,
          },
          {
            value: "short_answer",
            label: MESSAGE_KEYS.SEARCH_CONFIG.TYPE_SHORT_ANSWER,
          },
          {
            value: "essay",
            label: MESSAGE_KEYS.SEARCH_CONFIG.TYPE_ESSAY,
          },
          {
            value: "fill_in_blank",
            label: MESSAGE_KEYS.SEARCH_CONFIG.TYPE_FILL_IN_BLANK,
          },
        ],
      ),

      new MultipleSelectSearch(
        "difficulty",
        MESSAGE_KEYS.SEARCH_CONFIG.QUESTION_DIFFICULTY,
        [
          {
            value: "EASY",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_EASY,
          },
          {
            value: "MEDIUM",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_MEDIUM,
          },
          {
            value: "HARD",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_HARD,
          },
        ],
      ),

      new MultipleSelectSearch(
        "category",
        MESSAGE_KEYS.SEARCH_CONFIG.QUESTION_CATEGORY,
        [
          {
            value: "Programming",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_PROGRAMMING,
          },
          {
            value: "Mathematics",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_MATHEMATICS,
          },
          {
            value: "Science",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_SCIENCE,
          },
          {
            value: "Language",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_LANGUAGE,
          },
          {
            value: "General Knowledge",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_GENERAL_KNOWLEDGE,
          },
        ],
      ),

      new StringSearch("tags", MESSAGE_KEYS.SEARCH_CONFIG.QUESTION_TAGS, {
        validations: [
          (value: string) => {
            if (value.length == 0)
              throw new Error(ERROR_KEYS.TAGS_MUST_NOT_EMPTY)
          },
        ],
      }),

      new BooleanSearch("isActive", MESSAGE_KEYS.SEARCH_CONFIG.IS_ACTIVE),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["question", "explanation", "tags", "category"]

    const sorts = [
      new StringSort("question", MESSAGE_KEYS.SEARCH_CONFIG.SORT_QUESTION),
      new StringSort("category", MESSAGE_KEYS.SEARCH_CONFIG.SORT_CATEGORY),
      new StringSort("difficulty", MESSAGE_KEYS.SEARCH_CONFIG.SORT_DIFFICULTY),
      new StringSort("type", MESSAGE_KEYS.SEARCH_CONFIG.SORT_TYPE),
      new NumberSort("points", MESSAGE_KEYS.SEARCH_CONFIG.SORT_POINTS),
    ]

    super(filters, sorts, searchableFields, dateFilters)
  }
}

export const questionsSearchConfig = new QuestionsSearchConfig()
