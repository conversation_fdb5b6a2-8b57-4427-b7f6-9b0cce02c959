import MESSAGE_KEYS from "@/src/app/api/message_keys"
import { BooleanSearch } from "../filters/BooleanSearch"
import { MultipleSelectSearch } from "../filters/MultipleSelectSearch"
import { StringSearch } from "../filters/StringSearch"
import { NumberSearch } from "../filters/NumberSearch"
import { StringSort } from "../sorts/StringSort"
import { NumberSort } from "../sorts/NumberSort"
import {
  DateRangeSearchLastMonth,
  DateRangeSearchLastWeek,
  DateRangeSearchLastYear,
  DateRangeSearchThisMonth,
  DateRangeSearchThisWeek,
  DateRangeSearchThisYear,
  DateRangeSearchToday,
  DateRangeSearchYesterday,
} from "./common"
import { ERROR_KEYS } from "./errors"
import { SearchConfigEntity } from "./interface"
import { BaseSearchConfig } from "./baseSearchConfig"

export class TestsSearchConfig
  extends BaseSearchConfig
  implements SearchConfigEntity
{
  constructor() {
    const filters = [
      new StringSearch("title", MESSAGE_KEYS.SEARCH_CONFIG.TEST_TITLE, {
        validations: [
          (value: string) => {
            if (value.length == 0)
              throw new Error(ERROR_KEYS.TITLE_MUST_NOT_EMPTY)
          },
        ],
      }),

      new StringSearch(
        "description",
        MESSAGE_KEYS.SEARCH_CONFIG.TEST_DESCRIPTION,
        {
          validations: [
            (value: string) => {
              if (value.length == 0)
                throw new Error(ERROR_KEYS.DESCRIPTION_MUST_NOT_EMPTY)
            },
          ],
        },
      ),

      new MultipleSelectSearch(
        "category",
        MESSAGE_KEYS.SEARCH_CONFIG.TEST_CATEGORY,
        [
          {
            value: "Programming",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_PROGRAMMING,
          },
          {
            value: "Mathematics",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_MATHEMATICS,
          },
          {
            value: "Science",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_SCIENCE,
          },
          {
            value: "Language",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_LANGUAGE,
          },
          {
            value: "General Knowledge",
            label: MESSAGE_KEYS.SEARCH_CONFIG.CATEGORY_GENERAL_KNOWLEDGE,
          },
        ],
      ),

      new MultipleSelectSearch(
        "difficulty",
        MESSAGE_KEYS.SEARCH_CONFIG.TEST_DIFFICULTY,
        [
          {
            value: "EASY",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_EASY,
          },
          {
            value: "MEDIUM",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_MEDIUM,
          },
          {
            value: "HARD",
            label: MESSAGE_KEYS.SEARCH_CONFIG.DIFFICULTY_HARD,
          },
        ],
      ),

      new NumberSearch("timeLimit", MESSAGE_KEYS.SEARCH_CONFIG.TIME_LIMIT, {
        validations: [
          (value: number) => {
            if (value <= 0)
              throw new Error(ERROR_KEYS.TIME_LIMIT_MUST_BE_POSITIVE)
          },
        ],
      }),

      new NumberSearch("maxAttempts", MESSAGE_KEYS.SEARCH_CONFIG.MAX_ATTEMPTS, {
        validations: [
          (value: number) => {
            if (value <= 0)
              throw new Error(ERROR_KEYS.MAX_ATTEMPTS_MUST_BE_POSITIVE)
          },
        ],
      }),

      new StringSearch("tags", MESSAGE_KEYS.SEARCH_CONFIG.TEST_TAGS, {
        validations: [
          (value: string) => {
            if (value.length == 0)
              throw new Error(ERROR_KEYS.TAGS_MUST_NOT_EMPTY)
          },
        ],
      }),

      new BooleanSearch("isPublic", MESSAGE_KEYS.SEARCH_CONFIG.IS_PUBLIC),
      new BooleanSearch("isActive", MESSAGE_KEYS.SEARCH_CONFIG.IS_ACTIVE),
      new BooleanSearch(
        "randomizeQuestions",
        MESSAGE_KEYS.SEARCH_CONFIG.RANDOMIZE_QUESTIONS,
      ),
      new BooleanSearch("showResults", MESSAGE_KEYS.SEARCH_CONFIG.SHOW_RESULTS),
    ]

    const dateFilters = [
      DateRangeSearchToday,
      DateRangeSearchYesterday,
      DateRangeSearchThisWeek,
      DateRangeSearchLastWeek,
      DateRangeSearchThisMonth,
      DateRangeSearchLastMonth,
      DateRangeSearchThisYear,
      DateRangeSearchLastYear,
    ]

    const searchableFields = ["title", "description", "tags", "category"]

    const sorts = [
      new StringSort("title", MESSAGE_KEYS.SEARCH_CONFIG.SORT_TITLE),
      new StringSort("category", MESSAGE_KEYS.SEARCH_CONFIG.SORT_CATEGORY),
      new StringSort("difficulty", MESSAGE_KEYS.SEARCH_CONFIG.SORT_DIFFICULTY),
      new NumberSort("timeLimit", MESSAGE_KEYS.SEARCH_CONFIG.SORT_TIME_LIMIT),
      new NumberSort(
        "maxAttempts",
        MESSAGE_KEYS.SEARCH_CONFIG.SORT_MAX_ATTEMPTS,
      ),
      new NumberSort(
        "questionCount",
        MESSAGE_KEYS.SEARCH_CONFIG.SORT_QUESTION_COUNT,
      ),
    ]

    super(filters, sorts, searchableFields, dateFilters)
  }
}

export const testsSearchConfig = new TestsSearchConfig()
