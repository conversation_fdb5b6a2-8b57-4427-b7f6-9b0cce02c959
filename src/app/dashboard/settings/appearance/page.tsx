"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>, Sun, Moon, Smartphone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { SettingsAPI } from "@/src/services/settingsApi"
import { UserSettings } from "@/src/lib/repositories/settings/SettingsRepository"

export default function AppearancePage() {
  const [settings, setSettings] = useState<UserSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { toast } = useToast()

  // Get current user ID (in real app, this would come from auth context)
  const userId = "default-user"

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await SettingsAPI.UserSettings(userId).request()
      setSettings(data)
    } catch (error) {
      console.error("Failed to fetch settings:", error)
      toast({
        title: "Error",
        description: "Failed to load appearance settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateAppearanceSettings = async (updates: Partial<UserSettings>) => {
    if (!settings) return

    try {
      setSaving(true)
      const updatedSettings = await SettingsAPI.UpdateAppearanceSettings(
        userId,
        updates,
      ).request()
      setSettings(updatedSettings)
      toast({
        title: "Success",
        description: "Appearance settings updated successfully",
      })
    } catch (error) {
      console.error("Failed to update settings:", error)
      toast({
        title: "Error",
        description: "Failed to update appearance settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading || !settings) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Palette className="h-8 w-8" />
            Appearance
          </h1>
          <p className="text-muted-foreground">
            {loading ? "Loading..." : "Failed to load settings"}
          </p>
        </div>
      </div>
    )
  }

  const themes = [
    {
      value: "light",
      label: "Light",
      description: "Light mode with bright backgrounds",
      icon: Sun,
    },
    {
      value: "dark",
      label: "Dark",
      description: "Dark mode with dark backgrounds",
      icon: Moon,
    },
    {
      value: "system",
      label: "System",
      description: "Follow your system preference",
      icon: Monitor,
    },
  ]

  const colorSchemes = [
    { value: "blue", label: "Blue", color: "bg-blue-500" },
    { value: "green", label: "Green", color: "bg-green-500" },
    { value: "purple", label: "Purple", color: "bg-purple-500" },
    { value: "orange", label: "Orange", color: "bg-orange-500" },
    { value: "red", label: "Red", color: "bg-red-500" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Palette className="h-8 w-8" />
          Appearance
        </h1>
        <p className="text-muted-foreground">
          Customize the look and feel of your dashboard.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Theme */}
        <Card>
          <CardHeader>
            <CardTitle>Theme</CardTitle>
            <CardDescription>
              Choose your preferred theme for the dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={settings.theme}
              onValueChange={(value: "light" | "dark" | "system") =>
                updateAppearanceSettings({ theme: value })
              }
              className="grid grid-cols-1 md:grid-cols-3 gap-4"
              disabled={saving}
            >
              {themes.map((themeOption) => {
                const Icon = themeOption.icon
                return (
                  <div key={themeOption.value}>
                    <RadioGroupItem
                      value={themeOption.value}
                      id={themeOption.value}
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor={themeOption.value}
                      className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                    >
                      <Icon className="mb-3 h-6 w-6" />
                      <div className="text-center">
                        <div className="font-medium">{themeOption.label}</div>
                        <div className="text-sm text-muted-foreground">
                          {themeOption.description}
                        </div>
                      </div>
                    </Label>
                  </div>
                )
              })}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Color Scheme */}
        <Card>
          <CardHeader>
            <CardTitle>Color Scheme</CardTitle>
            <CardDescription>
              Choose your preferred accent color.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={settings.colorScheme}
              onValueChange={(
                value: "blue" | "green" | "purple" | "orange" | "red",
              ) => updateAppearanceSettings({ colorScheme: value })}
              className="grid grid-cols-5 gap-4"
              disabled={saving}
            >
              {colorSchemes.map((scheme) => (
                <div key={scheme.value}>
                  <RadioGroupItem
                    value={scheme.value}
                    id={scheme.value}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={scheme.value}
                    className="flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
                  >
                    <div
                      className={`w-8 h-8 rounded-full ${scheme.color} mb-2`}
                    />
                    <div className="text-sm font-medium">{scheme.label}</div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Display Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Display Settings</CardTitle>
            <CardDescription>
              Adjust how content is displayed in the dashboard.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="font-size">Font Size</Label>
              <Select
                value={settings.fontSize}
                onValueChange={(value: "small" | "MEDIUM" | "large") =>
                  updateAppearanceSettings({ fontSize: value })
                }
                disabled={saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select font size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Small</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="large">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="compact-mode">Compact Mode</Label>
                <div className="text-sm text-muted-foreground">
                  Reduce spacing and padding for a more compact layout
                </div>
              </div>
              <Switch
                id="compact-mode"
                checked={settings.compactMode}
                onCheckedChange={(checked) =>
                  updateAppearanceSettings({ compactMode: checked })
                }
                disabled={saving}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="animations">Animations</Label>
                <div className="text-sm text-muted-foreground">
                  Enable smooth transitions and animations
                </div>
              </div>
              <Switch
                id="animations"
                checked={settings.animations}
                onCheckedChange={(checked) =>
                  updateAppearanceSettings({ animations: checked })
                }
                disabled={saving}
              />
            </div>
          </CardContent>
        </Card>

        {/* Mobile Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Mobile Settings
            </CardTitle>
            <CardDescription>
              Settings specific to mobile devices.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-muted-foreground">
              Mobile-specific appearance settings will be available in a future
              update. The dashboard automatically adapts to your device screen
              size.
            </div>
          </CardContent>
        </Card>

        {/* Save Changes */}
        <div className="flex justify-end">
          <Button onClick={fetchSettings} disabled={saving}>
            {saving ? "Saving..." : "Refresh Settings"}
          </Button>
        </div>
      </div>
    </div>
  )
}
