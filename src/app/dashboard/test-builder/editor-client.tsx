"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { <PERSON>cil, Save, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ConfirmDialog } from "@/components/confirm-dialog"
import { TestSession } from "@/src/lib/test-types"
import { useLocalization } from "@/src/localization/functions/client"
import { Test } from "@/src/lib/repositories/tests/interface"
import {
  TestInformation,
  TestBenefits,
  TestQuestions,
  TestPreview,
  TestSessionSummary,
  TestActions,
  TestSessions,
  TestPremium,
  TestTopics,
} from "@/components/test-editor"
import { locales } from "./locales"

interface TestEditorProps {
  test: Test
  testSessions: TestSession[]
  onSave: (test: Test) => Promise<Test>
  onDelete?: () => Promise<void>
}

export function TestEditor({
  test: initialTest,
  testSessions,
  onSave,
  onDelete,
}: TestEditorProps) {
  const router = useRouter()
  const [test, setTest] = useState<Test>(initialTest)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showSuccessMessage, setShowSuccessMessage] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [jsonData, setJsonData] = useState("")
  const { t } = useLocalization("dashboard.test-builder", locales)

  useEffect(() => {
    if (isEditing && test) {
      setJsonData(JSON.stringify(test, null, 2))
    }
  }, [test, isEditing])

  const handleSave = async () => {
    try {
      const updatedTest = JSON.parse(jsonData)
      if (!updatedTest.title) {
        alert("Test title is required")
        return
      }

      setLoading(true)
      const savedTest = await onSave({
        ...updatedTest,
        updatedAt: Date.now(),
      })
      setTest(savedTest)
      setIsEditing(false)
      setShowSuccessMessage(true)
      setTimeout(() => setShowSuccessMessage(false), 3000)
    } catch (e) {
      console.error(e)
      alert("Invalid JSON format or API error")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div
      className="min-h-screen bg-sp-neutral-100 rounded-3xl"
      data-testid="test-editor-page"
    >
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-2">
            <ChevronRight className="h-4 w-4 text-gray-500" />
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{test.title}</h1>
              {test.premium?.isPremium && (
                <Badge className="bg-yellow-500 text-white text-xs px-2 py-0.5 rounded-full">
                  Premium
                </Badge>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant={isEditing ? "default" : "outline"}
              size="sm"
              onClick={() => (isEditing ? handleSave() : setIsEditing(true))}
              disabled={loading}
              data-testid={isEditing ? "save-test-button" : "edit-test-button"}
            >
              {isEditing ? (
                <>
                  <Save className="h-4 w-4 mr-2" /> {t("btn_save")}
                </>
              ) : (
                <>
                  <Pencil className="h-4 w-4 mr-2" /> {t("btn_detail_edit")}
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                router.push(`/dashboard/test-builder/t/${test.id}/questions`)
              }
            >
              {t("btn_manage_question")}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setDeleteDialogOpen(true)}
            >
              {t("btn_delete")}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="information" className="space-y-6">
          <TabsList className="inline-flex gap-2 w-auto overflow-x-auto whitespace-nowrap">
            <TabsTrigger value="information">{t("tab_info")}</TabsTrigger>
            <TabsTrigger value="benefits">{t("tab_benefits")}</TabsTrigger>
            <TabsTrigger value="premium">{t("tab_premium")}</TabsTrigger>
            <TabsTrigger value="topics">{t("tab_topics")}</TabsTrigger>
            <TabsTrigger value="questions">{t("tab_questions")}</TabsTrigger>
            <TabsTrigger value="preview">{t("tab_preview")}</TabsTrigger>
            <TabsTrigger value="sessions">
              {t("tab_sessions")}
              {testSessions.length > 0 && (
                <Badge className="ml-2 bg-orange-500 text-white text-xs px-1.5 py-0.5 min-w-0 h-5">
                  {testSessions.length}
                </Badge>
              )}
            </TabsTrigger>
            {/* <TabsTrigger value="actions">{t("tab_actions")}</TabsTrigger> */}
          </TabsList>

          <TabsContent value="information">
            <TestInformation
              test={test}
              isEditing={isEditing}
              onTestChange={setTest}
              t={t}
            />
          </TabsContent>

          <TabsContent value="benefits">
            <TestBenefits
              test={test}
              isEditing={isEditing}
              onTestChange={setTest}
              t={t}
            />
          </TabsContent>

          <TabsContent value="premium">
            {test.premium?.isPremium && (
              <div className="mb-4 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 p-4 rounded">
                This test is currently marked as <strong>Premium</strong>.
              </div>
            )}
            <TestPremium
              test={test}
              isEditing={isEditing}
              onTestChange={setTest}
              t={t}
            />
          </TabsContent>

          <TabsContent value="topics">
            <TestTopics
              test={test}
              isEditing={isEditing}
              onTestChange={setTest}
              t={t}
            />
          </TabsContent>

          <TabsContent value="questions">
            <TestQuestions
              test={test}
              onManageQuestions={() =>
                router.push(`/dashboard/test-builder/t/${test.id}/questions`)
              }
              t={t}
            />
          </TabsContent>

          <TabsContent value="preview">
            <TestPreview
              test={test}
              isEditing={isEditing}
              onTestChange={setTest}
              t={t}
            />
          </TabsContent>

          <TabsContent value="sessions">
            <TestSessions
              testSessions={testSessions}
              onReviewAnswers={(sessionId) =>
                router.push(`/dashboard/test-sessions/${sessionId}/review`)
              }
              onViewDetails={(sessionId) =>
                router.push(`/dashboard/test-sessions/${sessionId}`)
              }
              t={t}
            />
          </TabsContent>

          <TabsContent value="actions">
            <TestActions
              testId={test.id}
              onPreviewTest={() => router.push(`/test-preview/${test.id}`)}
              onManageQuestions={() =>
                router.push(`/dashboard/test-builder/t/${test.id}/questions`)
              }
              onDuplicateTest={() => {}}
              onDeleteTest={
                onDelete ? () => setDeleteDialogOpen(true) : undefined
              }
              t={t}
            />
            <TestSessionSummary testSessions={testSessions} t={t} />
          </TabsContent>
        </Tabs>
      </div>

      {showSuccessMessage && (
        <div className="fixed bottom-4 right-4">
          <Alert className="bg-green-600 text-white border-0">
            <AlertDescription>{t("alert_update_msg_success")}</AlertDescription>
          </Alert>
        </div>
      )}

      {onDelete && (
        <ConfirmDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          title={t("confirm_dialog_title")}
          description={t("confirm_dialog_desc")}
          confirmText={t("confirm_dialog_title")}
          cancelText={t("confirm_dialog_cancel")}
          onConfirm={onDelete}
          variant="destructive"
        />
      )}
    </div>
  )
}
