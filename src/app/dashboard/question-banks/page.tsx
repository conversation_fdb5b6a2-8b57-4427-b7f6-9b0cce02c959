"use client"

import { useEffect, useState } from "react"
import { QuestionsAPI } from "@/src/services/questionsApi"
import { Question } from "@/src/lib/repositories/questions/interface"
import {
  Search,
  Plus,
  Filter,
  Tag,
  ArrowUpDown,
  Check,
  X,
  LayoutGrid,
  List,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

// import TabsQuestionBankGrid from "@/components/layout/question-banks/TabsQuestionBankGrid";
// import TabsQuestionBankList from "@/components/layout/question-banks/TabsQuestionBankList";

import { useLocalization } from "@/src/localization/functions/client"
import { locales } from "./locales"
import TabsQuestionBankGrid from "@/components/layout/question-banks/TabsQuestionBankGrid"
import TabsQuestionBankList from "@/components/layout/question-banks/TabsQuestionBankList"
import { toast } from "@/hooks/use-toast"

export default function QuestionBanks() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [sortBy, setSortBy] = useState<keyof Question | "lastUpdated">(
    "lastUpdated",
  )
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showUngrouped, setShowUngrouped] = useState(true)
  const [loading, setLoading] = useState(true)

  const { t } = useLocalization("question-banks", locales)

  // Questions data (previously allItems)
  const [questions, setQuestions] = useState<Question[]>([])

  // Extract all unique tags from questions
  const allTags = Array.from(new Set(questions.flatMap((q) => q.tags || [])))

  const fetchData = async () => {
    setLoading(true)
    try {
      // Prepare filter params for QuestionAPI
      const filterTags = selectedTags.length > 0 ? selectedTags : undefined

      // Example mapping tab to difficulty or createdBy (adjust as per backend logic)
      // Here we just skip that because your API expects difficulty, tags, createdBy maybe?
      // So using tags & search only for demo.

      const params = {
        search: searchQuery || undefined,
        tags: filterTags,
        sortBy,
        sortOrder,
        // Add other filter keys if needed, e.g. difficulty, createdBy based on activeTab
      }

      const response = await QuestionsAPI.Questions({
        page: 1,
        per_page: 50,
      }).request()

      setQuestions(response.items || [])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch questions",
        variant: "destructive",
      })
      setQuestions([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (item: Question) => {
    setLoading(true)
    try {
      await QuestionsAPI.DeleteQuestion(item.id).request()
      setQuestions((prev) => prev.filter((q) => q.id !== item.id))
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete Question",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleClone = async (item: Question) => {
    setLoading(true)
    try {
      const response = await QuestionsAPI.CloneQuestion(item.id).request()
      setQuestions((prev) => [response, ...prev])
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clone Question",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [searchQuery, activeTab, sortBy, sortOrder, selectedTags, showUngrouped])

  const sortOptions = [
    { label: t("sortOptions.az"), sortBy: "question", sortOrder: "asc" },
    { label: t("sortOptions.za"), sortBy: "question", sortOrder: "desc" },
    { label: t("sortOptions.recent"), sortBy: "updatedAt", sortOrder: "desc" },
    { label: t("sortOptions.oldest"), sortBy: "updatedAt", sortOrder: "asc" },
  ]

  return (
    <div className="space-y-6" data-testid="question-bank-page">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t("title")}</h1>
        <div className="flex space-x-4">
          <Button asChild data-testid="create-question-button">
            <Link href={"/dashboard/question-editor/new"}>
              <Plus className="h-4 w-4 mr-2" />
              Create Question
            </Link>
          </Button>
          {/* <Button asChild>
            <Link href={"/dashboard/question-editor/group/new"}>
              <Plus className="h-4 w-4 mr-2" />
              Create Group
            </Link>
          </Button> */}
        </div>
      </div>

      {/* Filter & Search */}
      <Card className="shadow-none bg-sp-neutral-100 border-none rounded-3xl p-4 flex flex-col gap-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              data-testid="question-search-input"
              placeholder={t("searchPlaceholder")}
              className="pl-10 rounded-full"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            {/* Tags filter dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Filter
                  {selectedTags.length > 0 && (
                    <Badge variant="secondary" className="ml-1">
                      {selectedTags.length}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>{t("filterByTags")}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {allTags.map((tag) => (
                  <DropdownMenuItem
                    key={tag}
                    onClick={(e) => {
                      e.preventDefault()
                      setSelectedTags((prev) =>
                        prev.includes(tag)
                          ? prev.filter((t) => t !== tag)
                          : [...prev, tag],
                      )
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {selectedTags.includes(tag) ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <div className="w-4" />
                      )}
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </div>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowUngrouped(!showUngrouped)}
                >
                  <div className="flex items-center gap-2">
                    {showUngrouped ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <div className="w-4" />
                    )}
                    {t("showUngrouped")}
                  </div>
                </DropdownMenuItem>
                {selectedTags.length > 0 && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setSelectedTags([])}>
                      {t("clearFilters")}
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Sort dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  <ArrowUpDown className="h-4 w-4" />
                  {t("sort")}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t("sortBy")}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {sortOptions.map((option) => {
                  const isActive =
                    sortBy === option.sortBy && sortOrder === option.sortOrder
                  return (
                    <DropdownMenuItem
                      key={`${option.sortBy}-${option.sortOrder}`}
                      onClick={() => {
                        setSortBy(option.sortBy as any)
                        //@ts-ignore
                        setSortOrder(option.sortOrder)
                      }}
                    >
                      <div className="flex items-center gap-1">
                        {isActive && <Check className="h-3 w-3" />}
                        <div className={isActive ? "" : "w-4"} />
                        {option.label}
                      </div>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex justify-between items-center mb-2">
          <Tabs defaultValue="all" onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="all">{t("allBanks")}</TabsTrigger>
              <TabsTrigger value="shared">{t("shared")}</TabsTrigger>
              <TabsTrigger value="PRIVATE">{t("PRIVATE")}</TabsTrigger>
            </TabsList>
          </Tabs>

          {/* View Mode */}
          <div className="flex items-center border rounded-full">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="h-8 px-2 rounded-l-full rounded-r-md"
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
              <span className="sr-only">{t("gridView")}</span>
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              className="h-8 px-2 rounded-r-full rounded-l-md"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
              <span className="sr-only">{t("listView")}</span>
            </Button>
          </div>
        </div>

        {/* Active Filters Display */}
        {selectedTags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="cursor-pointer"
                onClick={() =>
                  setSelectedTags((prev) => prev.filter((t) => t !== tag))
                }
              >
                {tag} <X className="inline-block ml-1 h-3 w-3" />
              </Badge>
            ))}
          </div>
        )}
      </Card>

      {/* Questions List */}
      {loading ? (
        <p>Loading...</p>
      ) : viewMode === "grid" ? (
        <TabsQuestionBankGrid
          questions={questions}
          handleDelete={handleDelete}
          handleClone={handleClone}
        />
      ) : (
        <TabsQuestionBankList
          questions={questions}
          handleDelete={handleDelete}
          handleClone={handleClone}
        />
      )}
    </div>
  )
}
