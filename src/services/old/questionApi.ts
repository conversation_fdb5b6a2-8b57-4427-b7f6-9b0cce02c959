import { Question } from "@/src/lib/repositories/questions/types"
import { BaseAPI } from "../baseApi"
import { PagingAndSearch } from "../types"

type QuestionPayload = Partial<Question>

export class QuestionAPI extends BaseAPI {
  static Questions(
    params: PagingAndSearch<{
      difficulty?: "EASY" | "MEDIUM" | "HARD"
      tags?: string[]
      createdBy?: string
    }>,
  ) {
    const queryString = `?${new URLSearchParams(
      PagingAndSearch.toRecordString(params),
    ).toString()}`
    return new BaseAPI(`/questions${queryString}`).build<Question[]>()
  }

  static QuestionDetail = (questionId: string) =>
    new BaseAPI(`/questions/${questionId}`).build<Question>()

  static CreateQuestion = (body: QuestionPayload) =>
    new BaseAPI(`/questions`, body, "POST").build<{ id: string }>()

  static UpdateQuestion = (questionId: string, body: QuestionPayload) =>
    new BaseAPI(`/questions/${questionId}`, body, "PUT").build<Question>()

  static DeleteQuestion = (questionId: string) =>
    new BaseAPI(`/questions/${questionId}`, undefined, "DELETE").build<{
      success: boolean
    }>()

  static BulkCreateQuestions = (body: QuestionPayload[]) =>
    new BaseAPI(`/questions/bulk`, body, "POST").build<Question[]>()

  static BulkDeleteQuestions = (ids: string[]) =>
    new BaseAPI(`/questions/bulk-delete`, { ids }, "POST").build<{
      deleted: number
    }>()

  static CloneQuestion = (questionId: string) =>
    new BaseAPI(
      `/questions/${questionId}/clone`,
      undefined,
      "POST",
    ).build<Question>()
}
