import { SessionContext } from "../auth/types"

export type OptionText = {
  id: string
  text: string
}

export interface BaseQuestion {
  id: string
  question: string
  type: string
  correctAnswer?: string
  difficulty: string
  tags: string[]
  isActive: boolean
  organizationId: string
  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy: string
  updatedBy?: string
  internal?: Record<string, object>
  options?: OptionText[] // For multiple choice questions
}

export type OptionAudio = {
  id: string
  audio: string
}

export type MultipleChoiceQuestion = BaseQuestion & {
  type: "multipleChoice"
  options: OptionText[]
  correctAnswers: string[]
}

export type SingleChoiceQuestion = BaseQuestion & {
  type: "singleChoice"
  options: OptionText[]
  correctAnswer: string
}

export type TextInputQuestion = BaseQuestion & {
  type: "textInput"
  reviewGuide: string
  caseSensitive: boolean
}

export type TestCase = {
  input: string
  expectedOutput: string
}

export type CodeInputQuestion = BaseQuestion & {
  type: "codeInput"
  reviewGuide: {
    expectedOutput?: string
    requiredFunctions?: string[]
    comments?: string
  }

  language?: string
  correctAnswer?: string
  testCases?: TestCase[]
}

export type ImageBasedQuestion = BaseQuestion & {
  type: "imageBased"
  image: string
  options: OptionText[]
  correctAnswers: string[]
}

export type AudioBasedQuestion = BaseQuestion & {
  type: "audioBased"
  audio: string
  options: OptionText[]
  correctAnswers: string[]
}

export type AudioAnswerQuestion = BaseQuestion & {
  type: "audioAnswer"
  options: OptionAudio[]
  reviewGuide: string
}

export type VoiceInputQuestion = BaseQuestion & {
  type: "voiceInput"
  reviewGuide: string
  maxDuration?: number // in seconds
  correctPhrases?: string[]
}

export type FileUploadQuestion = BaseQuestion & {
  type: "fileUpload"
  reviewGuide: string

  allowedFileTypes?: string[]
  maxFileSize?: number // size in KB (or bytes if you prefer)
}

export type QuestionType =
  | "multipleChoice"
  | "singleChoice"
  | "textInput"
  | "codeInput"
  | "imageBased"
  | "audioBased"
  | "audioAnswer"
  | "voiceInput"
  | "fileUpload"

export type Question =
  | MultipleChoiceQuestion
  | SingleChoiceQuestion
  | TextInputQuestion
  | CodeInputQuestion
  | ImageBasedQuestion
  | AudioBasedQuestion
  | AudioAnswerQuestion
  | VoiceInputQuestion
  | FileUploadQuestion

export interface QuestionAnswer {
  id: string
  questionId: string
  answer: string[]
  isCorrect?: boolean
  reviewedAt?: string
}

export interface QuestionFilters {
  difficulty?: "EASY" | "MEDIUM" | "HARD"
  tags?: string[]
  createdBy?: string
  organizationId?: string
}

export type QuestionBankItemType = "QUESTION" | "QUESTION_GROUP"

export interface QuestionGroup {
  id: string
  type: "QUESTION_GROUP"
  name: string
  description: string
  tags: string[]
  questionCount: number
  updatedAt: Date
}

export type QuestionBank = QuestionGroup | Question

export type QuestionCreateInput = Partial<Question>
export type QuestionUpdateInput = Partial<Question>

export interface QuestionQueryParams {
  search?: string
  filters?: { field: keyof Question | string; value: any }[]
  sorts?: { field: keyof Question | string; direction: "asc" | "desc" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface QuestionBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Question | null>
  getAll(
    context: SessionContext,
    params?: QuestionQueryParams,
  ): Promise<{
    items: Question[]
    total: number
  }>
  create(data: QuestionCreateInput): Promise<Question>
  update(
    id: string,
    context: SessionContext,
    data: QuestionUpdateInput,
  ): Promise<Question | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>
  bulkCreate(data: QuestionCreateInput[]): Promise<Question[]>
  bulkUpdate(
    context: SessionContext,
    updates: { id: string; data: QuestionUpdateInput }[],
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
  clone(id: string, context: SessionContext): Promise<Question | null>
}
