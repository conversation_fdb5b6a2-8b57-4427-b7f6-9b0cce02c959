export interface UserSettings {
  id: string
  userId: string

  // Notification Settings
  emailNotifications: boolean
  pushNotifications: boolean
  courseUpdates: boolean
  testReminders: boolean
  collaboratorActivity: boolean
  notificationFrequency: "immediate" | "daily" | "weekly"

  // Appearance Settings
  theme: "light" | "dark" | "system"
  colorScheme: "blue" | "green" | "purple" | "orange" | "red"
  fontSize: "small" | "MEDIUM" | "large"
  compactMode: boolean
  animations: boolean

  // Language Settings
  language: string
  region: string
  dateFormat: string
  timeFormat: "12" | "24"

  // Security Settings
  twoFactorEnabled: boolean
  sessionTimeout: number // in minutes

  createdAt?: Date
  updatedAt?: Date
}

export interface SettingsResponse {
  settings: UserSettings
}

export interface PagingAndSearchParams {
  page?: number
  pageSize?: number
  search?: string
  [key: string]: any
}

export interface ISettingsRepository {
  getByUserId(userId: string): Promise<UserSettings | null>
  create(data: Partial<UserSettings>): Promise<UserSettings>
  update(
    userId: string,
    data: Partial<UserSettings>,
  ): Promise<UserSettings | null>
  delete(userId: string): Promise<boolean>

  // Specific setting updates
  updateNotificationSettings(
    userId: string,
    settings: Partial<
      Pick<
        UserSettings,
        | "emailNotifications"
        | "pushNotifications"
        | "courseUpdates"
        | "testReminders"
        | "collaboratorActivity"
        | "notificationFrequency"
      >
    >,
  ): Promise<UserSettings | null>
  updateAppearanceSettings(
    userId: string,
    settings: Partial<
      Pick<
        UserSettings,
        "theme" | "colorScheme" | "fontSize" | "compactMode" | "animations"
      >
    >,
  ): Promise<UserSettings | null>
  updateLanguageSettings(
    userId: string,
    settings: Partial<
      Pick<UserSettings, "language" | "region" | "dateFormat" | "timeFormat">
    >,
  ): Promise<UserSettings | null>
  updateSecuritySettings(
    userId: string,
    settings: Partial<
      Pick<UserSettings, "twoFactorEnabled" | "sessionTimeout">
    >,
  ): Promise<UserSettings | null>
}
